# Stage 2: 压感优化和高级功能开发计划

## 🎯 **Stage 2 总体目标**
**时间周期**: Week 3-4  
**核心任务**: 压感优化和高级功能开发

### **主要目标**
1. **Apple Pencil压感优化** - 更精确的压力检测和响应
2. **可编辑笔锋效果** - 动态笔锋形状和渐变
3. **部分擦除功能** - 智能擦除算法
4. **高级渲染效果** - 纹理、阴影、混合模式

## 📋 **当前压感实现状况分析**

### ✅ **已实现的基础功能**
1. **基础压感检测**
   ```swift
   private func getPressure(from touch: UITouch) -> Float {
       if touch.type == .pencil {
           return Float(touch.force / touch.maximumPossibleForce)
       } else {
           return 0.5  // 手指触摸固定值
       }
   }
   ```

2. **压感到线宽映射**
   ```swift
   func calculateWidth(for pressure: Float) -> Float {
       let normalizedPressure = max(0.0, min(1.0, pressure))
       let pressureEffect = pow(normalizedPressure, 1.0 / pressureSensitivity)
       let minWidthRatio: Float = 0.3
       let widthRatio = minWidthRatio + (1.0 - minWidthRatio) * pressureEffect
       return baseWidth * widthRatio
   }
   ```

3. **不同笔刷的压感敏感度**
   ```swift
   var defaultPressureSensitivity: Float {
       switch self {
       case .pen: return 0.8
       case .pencil: return 1.2
       case .marker: return 0.5
       case .brush: return 1.5
       case .highlighter: return 0.3
       }
   }
   ```

4. **可变宽度渲染**
   ```swift
   private static func renderVariableWidthStroke(_ stroke: Stroke, 
                                               in context: CGContext, 
                                               pressureFactor: CGFloat)
   ```

### ❌ **当前缺失的高级功能**
1. **Apple Pencil倾斜角度支持** - 未使用altitudeAngle和azimuthAngle
2. **压感预测和平滑** - 缺少智能压感处理
3. **动态笔锋效果** - 缺少笔锋形状变化
4. **部分擦除功能** - 完全缺失
5. **高级渲染效果** - 缺少纹理、阴影等

## 🔧 **Stage 2 具体开发任务**

### **Week 3: 压感优化和笔锋效果**

#### **Task 3.1: Apple Pencil高级压感支持**
- [ ] 实现倾斜角度检测 (altitudeAngle, azimuthAngle)
- [ ] 压感数据平滑和预测算法
- [ ] 自适应压感校准
- [ ] 压感历史分析和优化

#### **Task 3.2: 动态笔锋效果系统**
- [ ] 笔锋形状计算算法
- [ ] 基于倾斜角度的笔锋变化
- [ ] 笔锋渐变和过渡效果
- [ ] 可配置的笔锋参数

#### **Task 3.3: 高级渲染优化**
- [ ] 压感驱动的透明度变化
- [ ] 动态纹理强度调整
- [ ] 笔刷混合模式优化
- [ ] 实时渲染性能优化

### **Week 4: 擦除功能和高级效果**

#### **Task 4.1: 智能擦除系统**
- [ ] 部分擦除算法实现
- [ ] 擦除区域检测和计算
- [ ] 线条分割和重构
- [ ] 擦除历史和撤销支持

#### **Task 4.2: 高级视觉效果**
- [ ] 阴影和光晕效果
- [ ] 纹理映射系统
- [ ] 颜色混合和渐变
- [ ] 动画过渡效果

#### **Task 4.3: 性能优化和测试**
- [ ] 渲染性能优化
- [ ] 内存使用优化
- [ ] 用户体验测试
- [ ] 性能基准测试

## 📊 **技术实现重点**

### **1. Apple Pencil高级特性**
```swift
// 需要实现的高级压感处理
extension StrokeEngine {
    func processPencilInput(_ touch: UITouch) -> EnhancedStrokePoint {
        let pressure = touch.force / touch.maximumPossibleForce
        let altitude = touch.altitudeAngle  // 倾斜角度
        let azimuth = touch.azimuthAngle(in: view)  // 方位角
        
        return EnhancedStrokePoint(
            position: touch.location(in: view),
            pressure: Float(pressure),
            altitude: Float(altitude),
            azimuth: Float(azimuth),
            timestamp: touch.timestamp
        )
    }
}
```

### **2. 动态笔锋计算**
```swift
// 笔锋效果计算
struct BrushTip {
    let width: Float
    let height: Float
    let angle: Float
    let sharpness: Float
    
    static func calculate(from point: EnhancedStrokePoint, 
                         style: StrokeStyle) -> BrushTip {
        // 基于倾斜角度计算笔锋形状
        let aspectRatio = cos(point.altitude) * style.tipSensitivity
        return BrushTip(
            width: style.baseWidth * point.pressure,
            height: style.baseWidth * point.pressure * aspectRatio,
            angle: point.azimuth,
            sharpness: sin(point.altitude)
        )
    }
}
```

### **3. 智能擦除算法**
```swift
// 擦除功能实现
class EraseEngine {
    func eraseStroke(_ stroke: Stroke, 
                    in region: CGRect) -> [Stroke] {
        // 1. 检测相交区域
        let intersections = findIntersections(stroke, region)
        
        // 2. 分割线条
        let segments = splitStroke(stroke, at: intersections)
        
        // 3. 过滤擦除区域
        return segments.filter { !region.intersects($0.boundingBox) }
    }
}
```

## 🎯 **成功标准**

### **Week 3 里程碑**
- [ ] Apple Pencil倾斜角度正确检测和应用
- [ ] 动态笔锋效果在所有笔刷类型中正常工作
- [ ] 压感响应更加自然和精确
- [ ] 渲染性能保持在60FPS以上

### **Week 4 里程碑**
- [ ] 部分擦除功能完整实现
- [ ] 高级视觉效果（阴影、纹理）正常工作
- [ ] 所有功能通过用户体验测试
- [ ] 性能指标达到设计目标

## 📈 **预期改进效果**

### **压感体验提升**
- **响应精度**: 提升50%以上
- **自然度**: 接近真实笔刷体验
- **延迟**: 保持<16ms低延迟

### **视觉效果提升**
- **笔锋真实感**: 显著提升
- **渲染质量**: 专业级别
- **用户满意度**: 大幅提升

### **功能完整性**
- **擦除功能**: 完整的部分擦除支持
- **笔刷多样性**: 5种专业级笔刷
- **高级效果**: 纹理、阴影、混合模式

## 🚀 **开始实施**

现在让我们开始Stage 2的第一个任务：**Apple Pencil高级压感支持**的实现。
