//
//  PerformanceMonitor.swift
//  path_generator
//
//  Created by AI Assistant on 2025/5/27.
//

import Foundation
import UIKit

/// 性能监控器 - 监控应用的各项性能指标
class PerformanceMonitor: ObservableObject {
    // MARK: - Singleton
    static let shared = PerformanceMonitor()
    
    // MARK: - Published Properties
    @Published var currentFPS: Double = 0
    @Published var averageFPS: Double = 0
    @Published var inputLatency: TimeInterval = 0
    @Published var memoryUsage: Int64 = 0
    @Published var isMonitoring: Bool = false
    
    // MARK: - Private Properties
    private var displayLink: CADisplayLink?
    private var frameTimestamps: [TimeInterval] = []
    private var latencyMeasurements: [TimeInterval] = []
    private var operationTimings: [String: [TimeInterval]] = [:]
    private var operationStartTimes: [String: TimeInterval] = [:]
    
    private let maxSamples = 60  // 保留最近60帧的数据
    private let monitoringQueue = DispatchQueue(label: "performance.monitor", qos: .utility)
    
    // 内存监控
    private var memoryTimer: Timer?
    
    private init() {
        setupMemoryMonitoring()
    }
    
    deinit {
        stopMonitoring()
    }
    
    // MARK: - Public Methods
    
    /// 开始性能监控
    func startMonitoring() {
        guard !isMonitoring else { return }
        
        isMonitoring = true
        startFPSMonitoring()
        startMemoryMonitoring()
        
        print("📊 Performance monitoring started")
    }
    
    /// 停止性能监控
    func stopMonitoring() {
        guard isMonitoring else { return }
        
        isMonitoring = false
        stopFPSMonitoring()
        stopMemoryMonitoring()
        
        print("📊 Performance monitoring stopped")
    }
    
    /// 开始测量操作耗时
    /// - Parameter operation: 操作名称
    func startMeasuring(_ operation: String) {
        monitoringQueue.async { [weak self] in
            self?.operationStartTimes[operation] = CACurrentMediaTime()
        }
    }
    
    /// 结束测量操作耗时
    /// - Parameter operation: 操作名称
    func endMeasuring(_ operation: String) {
        let endTime = CACurrentMediaTime()
        
        monitoringQueue.async { [weak self] in
            guard let self = self,
                  let startTime = self.operationStartTimes[operation] else { return }
            
            let duration = endTime - startTime
            
            if self.operationTimings[operation] == nil {
                self.operationTimings[operation] = []
            }
            
            self.operationTimings[operation]?.append(duration)
            
            // 保持最近的测量数据
            if let timings = self.operationTimings[operation], timings.count > self.maxSamples {
                self.operationTimings[operation] = Array(timings.suffix(self.maxSamples))
            }
            
            self.operationStartTimes.removeValue(forKey: operation)
        }
    }
    
    /// 记录输入延迟
    /// - Parameter latency: 延迟时间
    func recordInputLatency(_ latency: TimeInterval) {
        monitoringQueue.async { [weak self] in
            guard let self = self else { return }
            
            self.latencyMeasurements.append(latency)
            
            // 保持最近的测量数据
            if self.latencyMeasurements.count > self.maxSamples {
                self.latencyMeasurements = Array(self.latencyMeasurements.suffix(self.maxSamples))
            }
            
            // 更新平均延迟
            DispatchQueue.main.async {
                self.inputLatency = self.latencyMeasurements.reduce(0, +) / Double(self.latencyMeasurements.count)
            }
        }
    }
    
    /// 获取操作的性能统计
    /// - Parameter operation: 操作名称
    /// - Returns: 性能统计信息
    func getOperationStats(_ operation: String) -> OperationStats? {
        return monitoringQueue.sync {
            guard let timings = operationTimings[operation], !timings.isEmpty else { return nil }
            
            let average = timings.reduce(0, +) / Double(timings.count)
            let minimum = timings.min() ?? 0
            let maximum = timings.max() ?? 0
            
            return OperationStats(
                operation: operation,
                sampleCount: timings.count,
                averageTime: average,
                minimumTime: minimum,
                maximumTime: maximum
            )
        }
    }
    
    /// 获取所有操作的性能统计
    /// - Returns: 所有操作的统计信息
    func getAllOperationStats() -> [OperationStats] {
        return monitoringQueue.sync {
            return operationTimings.compactMap { (operation, _) in
                getOperationStats(operation)
            }.sorted { $0.averageTime > $1.averageTime }
        }
    }
    
    /// 重置所有统计数据
    func resetStats() {
        monitoringQueue.async { [weak self] in
            self?.frameTimestamps.removeAll()
            self?.latencyMeasurements.removeAll()
            self?.operationTimings.removeAll()
            self?.operationStartTimes.removeAll()
        }
        
        DispatchQueue.main.async {
            self.currentFPS = 0
            self.averageFPS = 0
            self.inputLatency = 0
        }
        
        print("📊 Performance stats reset")
    }
    
    /// 生成性能报告
    func generateReport() -> PerformanceReport {
        let stats = getAllOperationStats()
        
        return PerformanceReport(
            currentFPS: currentFPS,
            averageFPS: averageFPS,
            inputLatency: inputLatency * 1000, // 转换为毫秒
            memoryUsage: memoryUsage,
            operationStats: stats
        )
    }
    
    /// 打印性能报告
    func logPerformanceReport() {
        let report = generateReport()
        
        print("""
        📊 Performance Report:
        - Current FPS: \(String(format: "%.1f", report.currentFPS))
        - Average FPS: \(String(format: "%.1f", report.averageFPS))
        - Input Latency: \(String(format: "%.2f", report.inputLatency))ms
        - Memory Usage: \(ByteCountFormatter().string(fromByteCount: report.memoryUsage))
        
        Operation Timings:
        """)
        
        for stat in report.operationStats {
            print("  - \(stat.operation): avg=\(String(format: "%.2f", stat.averageTime * 1000))ms, max=\(String(format: "%.2f", stat.maximumTime * 1000))ms")
        }
    }
    
    // MARK: - Private Methods
    
    /// 设置内存监控
    private func setupMemoryMonitoring() {
        // 初始内存读取
        updateMemoryUsage()
    }
    
    /// 开始FPS监控
    private func startFPSMonitoring() {
        displayLink = CADisplayLink(target: self, selector: #selector(displayLinkTick))
        displayLink?.add(to: .main, forMode: .common)
    }
    
    /// 停止FPS监控
    private func stopFPSMonitoring() {
        displayLink?.invalidate()
        displayLink = nil
    }
    
    /// 开始内存监控
    private func startMemoryMonitoring() {
        memoryTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.updateMemoryUsage()
        }
    }
    
    /// 停止内存监控
    private func stopMemoryMonitoring() {
        memoryTimer?.invalidate()
        memoryTimer = nil
    }
    
    /// DisplayLink回调
    @objc private func displayLinkTick() {
        let currentTime = CACurrentMediaTime()
        
        monitoringQueue.async { [weak self] in
            guard let self = self else { return }
            
            self.frameTimestamps.append(currentTime)
            
            // 保持最近的帧时间戳
            if self.frameTimestamps.count > self.maxSamples {
                self.frameTimestamps = Array(self.frameTimestamps.suffix(self.maxSamples))
            }
            
            // 计算FPS
            if self.frameTimestamps.count >= 2 {
                let timeDelta = currentTime - self.frameTimestamps[self.frameTimestamps.count - 2]
                let instantFPS = 1.0 / timeDelta
                
                // 计算平均FPS
                let totalTime = currentTime - self.frameTimestamps[0]
                let averageFPS = Double(self.frameTimestamps.count - 1) / totalTime
                
                DispatchQueue.main.async {
                    self.currentFPS = instantFPS
                    self.averageFPS = averageFPS
                }
            }
        }
    }
    
    /// 更新内存使用量
    private func updateMemoryUsage() {
        let usage = MemoryMonitor.shared.getCurrentMemoryUsage()
        
        DispatchQueue.main.async {
            self.memoryUsage = usage
        }
    }
}

// MARK: - Data Structures

/// 操作统计信息
struct OperationStats {
    let operation: String
    let sampleCount: Int
    let averageTime: TimeInterval
    let minimumTime: TimeInterval
    let maximumTime: TimeInterval
}

/// 性能报告
struct PerformanceReport {
    let currentFPS: Double
    let averageFPS: Double
    let inputLatency: TimeInterval  // 毫秒
    let memoryUsage: Int64
    let operationStats: [OperationStats]
}

// MARK: - Performance Alert System

/// 性能警报系统
class PerformanceAlertSystem {
    static let shared = PerformanceAlertSystem()
    
    // 阈值配置
    private let minFPS: Double = 55.0
    private let maxInputLatency: TimeInterval = 0.020  // 20ms
    private let maxMemoryUsage: Int64 = 200 * 1024 * 1024  // 200MB
    
    private var lastAlertTime: [String: TimeInterval] = [:]
    private let alertCooldown: TimeInterval = 5.0  // 5秒冷却时间
    
    private init() {}
    
    /// 检查性能指标并发出警报
    /// - Parameter report: 性能报告
    func checkPerformance(_ report: PerformanceReport) {
        let currentTime = CACurrentMediaTime()
        
        // 检查FPS
        if report.averageFPS < minFPS {
            sendAlert("low_fps", message: "Low FPS detected: \(String(format: "%.1f", report.averageFPS))", currentTime: currentTime)
        }
        
        // 检查输入延迟
        if report.inputLatency > maxInputLatency * 1000 {  // 转换为毫秒比较
            sendAlert("high_latency", message: "High input latency: \(String(format: "%.2f", report.inputLatency))ms", currentTime: currentTime)
        }
        
        // 检查内存使用
        if report.memoryUsage > maxMemoryUsage {
            let memoryMB = Double(report.memoryUsage) / (1024 * 1024)
            sendAlert("high_memory", message: "High memory usage: \(String(format: "%.1f", memoryMB))MB", currentTime: currentTime)
        }
    }
    
    private func sendAlert(_ type: String, message: String, currentTime: TimeInterval) {
        // 检查冷却时间
        if let lastTime = lastAlertTime[type], currentTime - lastTime < alertCooldown {
            return
        }
        
        lastAlertTime[type] = currentTime
        
        print("⚠️ Performance Alert [\(type)]: \(message)")
        
        // 这里可以发送通知或执行其他警报操作
        NotificationCenter.default.post(
            name: NSNotification.Name("PerformanceAlert"),
            object: nil,
            userInfo: ["type": type, "message": message]
        )
    }
}
