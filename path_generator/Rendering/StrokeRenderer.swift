//
//  StrokeRenderer.swift
//  path_generator
//
//  Created by AI Assistant on 2025/5/27.
//

import Foundation
import UIKit
import CoreGraphics

/// 线条渲染器 - 负责将线条数据渲染到Core Graphics上下文
class StrokeRenderer {

    // MARK: - 渲染配置
    private struct RenderConfig {
        static let minLineWidth: CGFloat = 0.5
        static let maxLineWidth: CGFloat = 20.0
        static let pressureSensitivity: CGFloat = 2.0
        static let smoothingFactor: CGFloat = 0.3
    }

    // MARK: - 公共方法

    /// 渲染单个线条
    /// - Parameters:
    ///   - stroke: 要渲染的线条
    ///   - context: Core Graphics上下文
    ///   - viewBounds: 视图边界
    static func renderStroke(_ stroke: Stroke, in context: CGContext, viewBounds: CGRect) {
        guard !stroke.points.isEmpty else { return }

        // 设置基础渲染属性
        setupRenderingContext(context, for: stroke)

        // 根据笔刷类型选择渲染方法
        switch stroke.style.brushType {
        case .pen:
            renderPenStroke(stroke, in: context)
        case .pencil:
            renderPencilStroke(stroke, in: context)
        case .marker:
            renderMarkerStroke(stroke, in: context)
        case .brush:
            renderBrushStroke(stroke, in: context)
        @unknown default:
            renderPenStroke(stroke, in: context) // 默认使用钢笔渲染
        }
    }

    /// 渲染多个线条
    /// - Parameters:
    ///   - strokes: 线条数组
    ///   - context: Core Graphics上下文
    ///   - viewBounds: 视图边界
    static func renderStrokes(_ strokes: [Stroke], in context: CGContext, viewBounds: CGRect) {
        for stroke in strokes {
            renderStroke(stroke, in: context, viewBounds: viewBounds)
        }
    }

    // MARK: - 私有渲染方法

    /// 设置渲染上下文
    private static func setupRenderingContext(_ context: CGContext, for stroke: Stroke) {
        context.saveGState()

        // 设置线条颜色
        let colorComponents = stroke.style.colorComponents
        context.setStrokeColor(red: CGFloat(colorComponents.r),
                              green: CGFloat(colorComponents.g),
                              blue: CGFloat(colorComponents.b),
                              alpha: CGFloat(colorComponents.a))
        context.setFillColor(red: CGFloat(colorComponents.r),
                            green: CGFloat(colorComponents.g),
                            blue: CGFloat(colorComponents.b),
                            alpha: CGFloat(colorComponents.a))

        // 设置透明度
        context.setAlpha(CGFloat(stroke.style.opacity))

        // 设置线条端点样式
        context.setLineCap(.round)
        context.setLineJoin(.round)

        // 设置抗锯齿
        context.setShouldAntialias(true)
        context.setAllowsAntialiasing(true)
    }

    /// 渲染钢笔线条
    private static func renderPenStroke(_ stroke: Stroke, in context: CGContext) {
        guard stroke.points.count >= 2 else {
            // 单点渲染
            if let point = stroke.points.first {
                renderSinglePoint(point, in: context, style: stroke.style)
            }
            return
        }

        // 创建路径
        let path = createSmoothPath(from: stroke.points)

        // 设置线宽（钢笔线宽相对固定，轻微压感变化）
        let baseWidth = stroke.style.baseWidth
        context.setLineWidth(CGFloat(baseWidth))

        // 绘制路径
        context.addPath(path)
        context.strokePath()

        context.restoreGState()
    }

    /// 渲染铅笔线条
    private static func renderPencilStroke(_ stroke: Stroke, in context: CGContext) {
        guard stroke.points.count >= 2 else {
            if let point = stroke.points.first {
                renderSinglePoint(point, in: context, style: stroke.style)
            }
            return
        }

        // 铅笔效果：压感变化更明显，有纹理感
        renderVariableWidthStroke(stroke, in: context, pressureFactor: 1.5)

        context.restoreGState()
    }

    /// 渲染马克笔线条
    private static func renderMarkerStroke(_ stroke: Stroke, in context: CGContext) {
        guard stroke.points.count >= 2 else {
            if let point = stroke.points.first {
                renderSinglePoint(point, in: context, style: stroke.style)
            }
            return
        }

        // 马克笔效果：宽线条，半透明，压感变化中等
        context.setAlpha(CGFloat(stroke.style.opacity) * 0.7) // 更透明
        renderVariableWidthStroke(stroke, in: context, pressureFactor: 1.0)

        context.restoreGState()
    }

    /// 渲染毛笔线条
    private static func renderBrushStroke(_ stroke: Stroke, in context: CGContext) {
        guard stroke.points.count >= 2 else {
            if let point = stroke.points.first {
                renderSinglePoint(point, in: context, style: stroke.style)
            }
            return
        }

        // 毛笔效果：压感变化最大，有笔锋效果
        renderVariableWidthStroke(stroke, in: context, pressureFactor: 2.5)

        context.restoreGState()
    }



    /// 渲染可变宽度线条
    private static func renderVariableWidthStroke(_ stroke: Stroke, in context: CGContext, pressureFactor: CGFloat) {
        let points = stroke.points
        guard points.count >= 2 else { return }

        // 分段渲染，每段根据压力调整宽度
        for i in 0..<(points.count - 1) {
            let startPoint = points[i]
            let endPoint = points[i + 1]

            // 计算压力影响的线宽
            let startWidth = calculateLineWidth(
                baseWidth: stroke.style.baseWidth,
                pressure: startPoint.pressure,
                pressureFactor: pressureFactor
            )
            let endWidth = calculateLineWidth(
                baseWidth: stroke.style.baseWidth,
                pressure: endPoint.pressure,
                pressureFactor: pressureFactor
            )

            // 渲染渐变宽度的线段
            renderGradientWidthSegment(
                from: startPoint,
                to: endPoint,
                startWidth: startWidth,
                endWidth: endWidth,
                in: context
            )
        }
    }

    /// 渲染渐变宽度线段
    private static func renderGradientWidthSegment(
        from startPoint: StrokePoint,
        to endPoint: StrokePoint,
        startWidth: CGFloat,
        endWidth: CGFloat,
        in context: CGContext
    ) {
        let startCGPoint = CGPoint(x: CGFloat(startPoint.x), y: CGFloat(startPoint.y))
        let endCGPoint = CGPoint(x: CGFloat(endPoint.x), y: CGFloat(endPoint.y))

        // 计算垂直向量
        let dx = endCGPoint.x - startCGPoint.x
        let dy = endCGPoint.y - startCGPoint.y
        let length = sqrt(dx * dx + dy * dy)

        guard length > 0 else { return }

        let unitX = -dy / length
        let unitY = dx / length

        // 创建四边形路径
        let path = CGMutablePath()

        let startOffset = startWidth / 2
        let endOffset = endWidth / 2

        // 四个顶点
        let p1 = CGPoint(x: startCGPoint.x + unitX * startOffset, y: startCGPoint.y + unitY * startOffset)
        let p2 = CGPoint(x: startCGPoint.x - unitX * startOffset, y: startCGPoint.y - unitY * startOffset)
        let p3 = CGPoint(x: endCGPoint.x - unitX * endOffset, y: endCGPoint.y - unitY * endOffset)
        let p4 = CGPoint(x: endCGPoint.x + unitX * endOffset, y: endCGPoint.y + unitY * endOffset)

        path.move(to: p1)
        path.addLine(to: p2)
        path.addLine(to: p3)
        path.addLine(to: p4)
        path.closeSubpath()

        context.addPath(path)
        context.fillPath()
    }

    /// 计算基于压力的线宽
    private static func calculateLineWidth(baseWidth: Float, pressure: Float, pressureFactor: CGFloat) -> CGFloat {
        let pressureMultiplier = CGFloat(pressure) * pressureFactor
        let calculatedWidth = CGFloat(baseWidth) * (0.3 + pressureMultiplier * 0.7)
        return max(RenderConfig.minLineWidth, min(RenderConfig.maxLineWidth, calculatedWidth))
    }

    /// 创建平滑路径 - 修复断续问题
    private static func createSmoothPath(from points: [StrokePoint]) -> CGPath {
        let path = CGMutablePath()
        guard !points.isEmpty else { return path }

        // 单点处理
        if points.count == 1 {
            let point = CGPoint(x: CGFloat(points[0].x), y: CGFloat(points[0].y))
            path.move(to: point)
            path.addLine(to: point) // 确保单点也能渲染
            return path
        }

        let firstPoint = CGPoint(x: CGFloat(points[0].x), y: CGFloat(points[0].y))
        path.move(to: firstPoint)

        // 简化路径构造，避免复杂的贝塞尔曲线导致断续
        for i in 1..<points.count {
            let currentPoint = CGPoint(x: CGFloat(points[i].x), y: CGFloat(points[i].y))
            path.addLine(to: currentPoint)
        }

        return path
    }

    /// 渲染单个点
    private static func renderSinglePoint(_ point: StrokePoint, in context: CGContext, style: StrokeStyle) {
        let cgPoint = CGPoint(x: CGFloat(point.x), y: CGFloat(point.y))
        let radius = CGFloat(style.baseWidth) / 2

        context.fillEllipse(in: CGRect(
            x: cgPoint.x - radius,
            y: cgPoint.y - radius,
            width: radius * 2,
            height: radius * 2
        ))
    }
}

// MARK: - 渲染优化扩展
extension StrokeRenderer {

    /// 检查线条是否在视图范围内
    static func isStrokeVisible(_ stroke: Stroke, in viewBounds: CGRect) -> Bool {
        let bounds = stroke.boundingBox

        let strokeBounds = CGRect(
            x: CGFloat(bounds.minX),
            y: CGFloat(bounds.minY),
            width: CGFloat(bounds.width),
            height: CGFloat(bounds.height)
        )

        return viewBounds.intersects(strokeBounds)
    }

    /// 获取需要重绘的区域
    static func getRedrawRegion(for stroke: Stroke, lineWidth: CGFloat) -> CGRect {
        let bounds = stroke.boundingBox

        let margin = lineWidth + 2 // 额外边距

        return CGRect(
            x: CGFloat(bounds.minX) - margin,
            y: CGFloat(bounds.minY) - margin,
            width: CGFloat(bounds.width) + margin * 2,
            height: CGFloat(bounds.height) + margin * 2
        )
    }
}
