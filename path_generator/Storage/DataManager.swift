//
//  DataManager.swift
//  path_generator
//
//  Created by AI Assistant on 2024/12/27.
//

import Foundation
import Combine

/// 数据管理器 - 负责线条数据的存储和管理
class DataManager: ObservableObject {

    // MARK: - Published Properties
    @Published var strokes: [Stroke] = []
    @Published var isLoading: Bool = false
    @Published var isSaving: Bool = false

    // MARK: - Private Properties
    private let fileManager = FileManager.default
    private let documentsDirectory: URL
    private let strokesFileName = "strokes.json"
    private var cancellables = Set<AnyCancellable>()

    // 内存管理
    private let maxStrokesInMemory = 1000
    private var strokeCache: [UUID: Stroke] = [:]

    // 性能监控
    private var saveQueue = DispatchQueue(label: "data.save", qos: .utility)
    private var loadQueue = DispatchQueue(label: "data.load", qos: .userInitiated)

    // MARK: - 初始化

    init() {
        // 获取文档目录
        documentsDirectory = fileManager.urls(for: .documentDirectory,
                                            in: .userDomainMask).first!

        // 自动保存设置
        setupAutoSave()
    }

    // MARK: - 公共方法

    /// 添加新线条
    /// - Parameter stroke: 要添加的线条
    func addStroke(_ stroke: Stroke) {
        // 确保在主线程中修改 @Published 属性
        if Thread.isMainThread {
            strokes.append(stroke)

            // 内存管理：如果超过限制，移除最旧的线条
            if strokes.count > maxStrokesInMemory {
                let oldestStroke = strokes.removeFirst()
                strokeCache.removeValue(forKey: oldestStroke.id)
            }
        } else {
            DispatchQueue.main.async {
                self.strokes.append(stroke)

                // 内存管理：如果超过限制，移除最旧的线条
                if self.strokes.count > self.maxStrokesInMemory {
                    let oldestStroke = self.strokes.removeFirst()
                    self.strokeCache.removeValue(forKey: oldestStroke.id)
                }
            }
        }

        strokeCache[stroke.id] = stroke
        print("📝 Added stroke \(stroke.id), total: \(strokes.count)")
    }

    /// 移除线条
    /// - Parameter strokeId: 线条ID
    func removeStroke(id strokeId: UUID) {
        strokes.removeAll { $0.id == strokeId }
        strokeCache.removeValue(forKey: strokeId)

        print("🗑️ Removed stroke \(strokeId), total: \(strokes.count)")
    }

    /// 清除所有线条
    func clearAllStrokes() {
        DispatchQueue.main.async {
            self.strokes.removeAll()
        }
        strokeCache.removeAll()

        print("🧹 Cleared all strokes")
    }

    /// 移除最后一条线条
    /// - Returns: 被移除的线条，如果没有线条则返回nil
    func removeLastStroke() -> Stroke? {
        guard !strokes.isEmpty else { return nil }
        let removedStroke: Stroke

        // 在主线程中修改 @Published 属性
        if Thread.isMainThread {
            removedStroke = strokes.removeLast()
        } else {
            var tempStroke: Stroke!
            DispatchQueue.main.sync {
                tempStroke = self.strokes.removeLast()
            }
            removedStroke = tempStroke
        }

        strokeCache.removeValue(forKey: removedStroke.id)
        print("↩️ Removed last stroke with \(removedStroke.pointCount) points")
        return removedStroke
    }

    /// 获取线条
    /// - Parameter id: 线条ID
    /// - Returns: 线条对象
    func getStroke(id: UUID) -> Stroke? {
        return strokeCache[id] ?? strokes.first { $0.id == id }
    }

    /// 保存数据到文件
    func saveData() {
        guard !isSaving else { return }

        isSaving = true

        saveQueue.async { [weak self] in
            guard let self = self else { return }

            do {
                let data = try JSONEncoder().encode(self.strokes)
                let url = self.documentsDirectory.appendingPathComponent(self.strokesFileName)
                try data.write(to: url)

                DispatchQueue.main.async {
                    self.isSaving = false
                    print("💾 Saved \(self.strokes.count) strokes to file")
                }
            } catch {
                DispatchQueue.main.async {
                    self.isSaving = false
                    print("❌ Failed to save data: \(error)")
                }
            }
        }
    }

    /// 从文件加载数据
    func loadData() {
        guard !isLoading else { return }

        isLoading = true

        loadQueue.async { [weak self] in
            guard let self = self else { return }

            do {
                let url = self.documentsDirectory.appendingPathComponent(self.strokesFileName)
                let data = try Data(contentsOf: url)
                let loadedStrokes = try JSONDecoder().decode([Stroke].self, from: data)

                DispatchQueue.main.async {
                    self.strokes = loadedStrokes

                    // 重建缓存
                    self.strokeCache.removeAll()
                    for stroke in loadedStrokes {
                        self.strokeCache[stroke.id] = stroke
                    }

                    self.isLoading = false
                    print("📂 Loaded \(loadedStrokes.count) strokes from file")
                }
            } catch {
                DispatchQueue.main.async {
                    self.isLoading = false
                    print("❌ Failed to load data: \(error)")
                }
            }
        }
    }

    /// 检查文件是否存在
    var hasExistingData: Bool {
        let url = documentsDirectory.appendingPathComponent(strokesFileName)
        return fileManager.fileExists(atPath: url.path)
    }

    /// 获取数据文件大小
    var dataFileSize: Int64 {
        let url = documentsDirectory.appendingPathComponent(strokesFileName)
        do {
            let attributes = try fileManager.attributesOfItem(atPath: url.path)
            return attributes[.size] as? Int64 ?? 0
        } catch {
            return 0
        }
    }

    // MARK: - 私有方法

    /// 设置自动保存
    private func setupAutoSave() {
        // 每当strokes数组变化时，延迟保存
        $strokes
            .debounce(for: .seconds(2), scheduler: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.saveData()
            }
            .store(in: &cancellables)
    }

    /// 获取统计信息
    var statistics: DataStatistics {
        let totalPoints = strokes.reduce(0) { $0 + $1.pointCount }
        let averagePointsPerStroke = strokes.isEmpty ? 0 : totalPoints / strokes.count

        return DataStatistics(
            totalStrokes: strokes.count,
            totalPoints: totalPoints,
            averagePointsPerStroke: averagePointsPerStroke,
            memoryUsage: strokeCache.count,
            fileSize: dataFileSize
        )
    }
}

// MARK: - 支持类型

/// 数据统计信息
struct DataStatistics {
    let totalStrokes: Int
    let totalPoints: Int
    let averagePointsPerStroke: Int
    let memoryUsage: Int
    let fileSize: Int64

    var fileSizeFormatted: String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: fileSize)
    }
}

// MARK: - 扩展：批量操作

extension DataManager {

    /// 批量添加线条
    /// - Parameter newStrokes: 要添加的线条数组
    func addStrokes(_ newStrokes: [Stroke]) {
        strokes.append(contentsOf: newStrokes)

        // 更新缓存
        for stroke in newStrokes {
            strokeCache[stroke.id] = stroke
        }

        // 内存管理
        while strokes.count > maxStrokesInMemory {
            let oldestStroke = strokes.removeFirst()
            strokeCache.removeValue(forKey: oldestStroke.id)
        }

        print("📝 Added \(newStrokes.count) strokes, total: \(strokes.count)")
    }

    /// 批量移除线条
    /// - Parameter strokeIds: 要移除的线条ID数组
    func removeStrokes(ids strokeIds: [UUID]) {
        let idsSet = Set(strokeIds)
        strokes.removeAll { idsSet.contains($0.id) }

        for id in strokeIds {
            strokeCache.removeValue(forKey: id)
        }

        print("🗑️ Removed \(strokeIds.count) strokes, total: \(strokes.count)")
    }

    /// 查找包含指定点的线条
    /// - Parameter point: 查找点
    /// - Returns: 包含该点的线条数组
    func findStrokes(containing point: CGPoint) -> [Stroke] {
        return strokes.filter { stroke in
            stroke.boundingBox.contains(point)
        }
    }

    /// 查找在指定区域内的线条
    /// - Parameter rect: 查找区域
    /// - Returns: 在该区域内的线条数组
    func findStrokes(in rect: CGRect) -> [Stroke] {
        return strokes.filter { stroke in
            stroke.boundingBox.intersects(rect)
        }
    }
}
