//
//  CanvasView.swift
//  path_generator
//
//  Created by AI Assistant on 2025/5/26.
//

import SwiftUI
import PencilKit

/// 主绘图画布视图
struct CanvasView: UIViewRepresentable {
    // MARK: - 绑定属性
    @Binding var strokes: [Stroke]
    @Binding var currentStyle: StrokeStyle
    @Binding var isDrawingEnabled: Bool
    
    // MARK: - 回调
    var onStrokeStarted: ((CGPoint, Float) -> Void)?
    var onStrokeUpdated: ((CGPoint, Float) -> Void)?
    var onStrokeEnded: (() -> Void)?
    
    // MARK: - UIViewRepresentable 实现
    func makeUIView(context: Context) -> DrawingCanvasView {
        let canvasView = DrawingCanvasView()
        canvasView.delegate = context.coordinator
        canvasView.isDrawingEnabled = isDrawingEnabled
        canvasView.currentStyle = currentStyle
        return canvasView
    }
    
    func updateUIView(_ uiView: DrawingCanvasView, context: Context) {
        uiView.isDrawingEnabled = isDrawingEnabled
        uiView.currentStyle = currentStyle
        uiView.updateStrokes(strokes)
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    // MARK: - Coordinator
    class Coordinator: NSObject, DrawingCanvasDelegate {
        var parent: CanvasView
        
        init(_ parent: CanvasView) {
            self.parent = parent
        }
        
        func strokeStarted(at point: CGPoint, pressure: Float) {
            parent.onStrokeStarted?(point, pressure)
        }
        
        func strokeUpdated(at point: CGPoint, pressure: Float) {
            parent.onStrokeUpdated?(point, pressure)
        }
        
        func strokeEnded() {
            parent.onStrokeEnded?()
        }
        
        func strokesChanged(_ strokes: [Stroke]) {
            DispatchQueue.main.async {
                self.parent.strokes = strokes
            }
        }
    }
}

// MARK: - 绘图画布委托协议
protocol DrawingCanvasDelegate: AnyObject {
    func strokeStarted(at point: CGPoint, pressure: Float)
    func strokeUpdated(at point: CGPoint, pressure: Float)
    func strokeEnded()
    func strokesChanged(_ strokes: [Stroke])
}

// MARK: - 自定义绘图画布
class DrawingCanvasView: UIView {
    // MARK: - 属性
    weak var delegate: DrawingCanvasDelegate?
    
    var isDrawingEnabled: Bool = true {
        didSet {
            isUserInteractionEnabled = isDrawingEnabled
        }
    }
    
    var currentStyle: StrokeStyle = .defaultPen
    
    private var displayedStrokes: [Stroke] = []
    private var currentStroke: Stroke?
    private var lastPoint: CGPoint?
    private var lastTimestamp: TimeInterval = 0
    
    // MARK: - 初始化
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }
    
    private func setupView() {
        backgroundColor = UIColor.systemBackground
        isMultipleTouchEnabled = false
        
        // 启用Apple Pencil支持
        if #available(iOS 12.9, *) {
            // 配置Apple Pencil设置
        }
    }
    
    // MARK: - 公共方法
    func updateStrokes(_ strokes: [Stroke]) {
        displayedStrokes = strokes
        setNeedsDisplay()
    }
    
    func clearCanvas() {
        displayedStrokes.removeAll()
        currentStroke = nil
        setNeedsDisplay()
    }
    
    // MARK: - 绘制
    override func draw(_ rect: CGRect) {
        guard let context = UIGraphicsGetCurrentContext() else { return }
        
        // 清空背景
        context.setFillColor(UIColor.systemBackground.cgColor)
        context.fill(rect)
        
        // 绘制所有完成的线条
        for stroke in displayedStrokes {
            drawStroke(stroke, in: context)
        }
        
        // 绘制当前正在绘制的线条
        if let currentStroke = currentStroke {
            drawStroke(currentStroke, in: context)
        }
    }
    
    private func drawStroke(_ stroke: Stroke, in context: CGContext) {
        guard !stroke.points.isEmpty else { return }
        
        // 只绘制未擦除的分段
        let activeSegments = stroke.activeSegments
        
        for segment in activeSegments {
            drawSegment(segment, of: stroke, in: context)
        }
    }
    
    private func drawSegment(_ segment: StrokeSegment, of stroke: Stroke, in context: CGContext) {
        guard segment.isValid && segment.startIndex < stroke.points.count && segment.endIndex < stroke.points.count else {
            return
        }
        
        let points = Array(stroke.points[segment.startIndex...segment.endIndex])
        guard points.count >= 2 else {
            // 单点绘制
            if let point = points.first {
                drawPoint(point, style: stroke.style, in: context)
            }
            return
        }
        
        // 设置绘制属性
        let colorComponents = stroke.style.colorComponents
        context.setStrokeColor(red: CGFloat(colorComponents.r), 
                              green: CGFloat(colorComponents.g), 
                              blue: CGFloat(colorComponents.b), 
                              alpha: CGFloat(colorComponents.a * stroke.style.opacity))
        
        context.setLineCap(.round)
        context.setLineJoin(.round)
        
        // 绘制路径
        context.beginPath()
        
        let firstPoint = points[0]
        context.move(to: firstPoint.cgPoint)
        
        for i in 1..<points.count {
            let point = points[i]
            let width = stroke.style.calculateWidth(for: point.pressure)
            
            context.setLineWidth(CGFloat(width))
            context.addLine(to: point.cgPoint)
            context.strokePath()
            
            // 为下一段做准备
            context.beginPath()
            context.move(to: point.cgPoint)
        }
    }
    
    private func drawPoint(_ point: StrokePoint, style: StrokeStyle, in context: CGContext) {
        let width = style.calculateWidth(for: point.pressure)
        let colorComponents = style.colorComponents
        
        context.setFillColor(red: CGFloat(colorComponents.r), 
                            green: CGFloat(colorComponents.g), 
                            blue: CGFloat(colorComponents.b), 
                            alpha: CGFloat(colorComponents.a * style.opacity))
        
        let rect = CGRect(
            x: CGFloat(point.x) - CGFloat(width) / 2,
            y: CGFloat(point.y) - CGFloat(width) / 2,
            width: CGFloat(width),
            height: CGFloat(width)
        )
        
        context.fillEllipse(in: rect)
    }
}

// MARK: - 触摸处理
extension DrawingCanvasView {
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard isDrawingEnabled, let touch = touches.first else { return }
        
        let location = touch.location(in: self)
        let pressure = getPressure(from: touch)
        let timestamp = CACurrentMediaTime()
        
        // 创建新线条
        currentStroke = Stroke(style: currentStyle)
        let point = StrokePoint(point: location, pressure: pressure, timestamp: timestamp)
        currentStroke?.addPoint(point)
        
        lastPoint = location
        lastTimestamp = timestamp
        
        delegate?.strokeStarted(at: location, pressure: pressure)
        setNeedsDisplay()
    }
    
    override func touchesMoved(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard isDrawingEnabled, let touch = touches.first, let stroke = currentStroke else { return }
        
        let location = touch.location(in: self)
        let pressure = getPressure(from: touch)
        let timestamp = CACurrentMediaTime()
        
        // 基础过滤 - 避免过于密集的点
        if let lastPoint = lastPoint {
            let distance = sqrt(pow(location.x - lastPoint.x, 2) + pow(location.y - lastPoint.y, 2))
            let timeDelta = timestamp - lastTimestamp
            
            // 如果距离太近或时间间隔太短，跳过此点
            if distance < 2.0 && timeDelta < 0.008 { // 125 FPS 最大
                return
            }
        }
        
        let point = StrokePoint(point: location, pressure: pressure, timestamp: timestamp)
        stroke.addPoint(point)
        
        self.lastPoint = location
        self.lastTimestamp = timestamp
        
        delegate?.strokeUpdated(at: location, pressure: pressure)
        
        // 只重绘需要更新的区域
        setNeedsDisplayInRegion(around: location)
    }
    
    override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard isDrawingEnabled else { return }
        
        if let stroke = currentStroke {
            stroke.complete()
            displayedStrokes.append(stroke)
            delegate?.strokesChanged(displayedStrokes)
        }
        
        currentStroke = nil
        lastPoint = nil
        
        delegate?.strokeEnded()
        setNeedsDisplay()
    }
    
    override func touchesCancelled(_ touches: Set<UITouch>, with event: UIEvent?) {
        touchesEnded(touches, with: event)
    }
    
    private func getPressure(from touch: UITouch) -> Float {
        if touch.type == .pencil {
            // Apple Pencil 支持压感
            return Float(touch.force / touch.maximumPossibleForce)
        } else {
            // 手指触摸，使用固定压力值
            return 0.5
        }
    }
    
    private func setNeedsDisplayInRegion(around point: CGPoint) {
        let width = CGFloat(currentStyle.baseWidth * 4) // 留出足够的边距
        let rect = CGRect(
            x: point.x - width,
            y: point.y - width,
            width: width * 2,
            height: width * 2
        )
        setNeedsDisplay(rect)
    }
}

// MARK: - 预览支持
struct CanvasView_Previews: PreviewProvider {
    @State static var strokes: [Stroke] = []
    @State static var currentStyle = StrokeStyle.defaultPen
    @State static var isDrawingEnabled = true
    
    static var previews: some View {
        CanvasView(
            strokes: $strokes,
            currentStyle: $currentStyle,
            isDrawingEnabled: $isDrawingEnabled
        )
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.white)
    }
}
