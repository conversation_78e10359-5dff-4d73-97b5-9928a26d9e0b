//
//  CanvasView.swift
//  path_generator
//
//  Created by AI Assistant on 2025/5/26.
//

import SwiftUI
import PencilKit

/// 主绘图画布视图
struct CanvasView: UIViewRepresentable {
    // MARK: - 绑定属性
    @Binding var strokes: [Stroke]
    @Binding var currentStyle: StrokeStyle
    @Binding var isDrawingEnabled: Bool
    @Binding var clearTrigger: Bool

    // MARK: - 回调
    var onStrokeStarted: ((CGPoint, Float) -> Void)?
    var onStrokeUpdated: ((CGPoint, Float) -> Void)?
    var onStrokeEnded: (() -> Void)?

    // MARK: - 引擎
    @StateObject private var strokeEngine = StrokeEngine()
    @StateObject private var dataManager = DataManager()
    @StateObject private var renderEngine = RenderEngine()

    // MARK: - UIViewRepresentable 实现
    func makeUIView(context: Context) -> DrawingCanvasView {
        let canvasView = DrawingCanvasView()

        // 设置依赖关系
        strokeEngine.dataManager = dataManager
        renderEngine.dataManager = dataManager

        canvasView.delegate = context.coordinator
        canvasView.strokeEngine = strokeEngine
        canvasView.renderEngine = renderEngine
        canvasView.isDrawingEnabled = isDrawingEnabled
        canvasView.currentStyle = currentStyle
        return canvasView
    }

    func updateUIView(_ uiView: DrawingCanvasView, context: Context) {
        uiView.isDrawingEnabled = isDrawingEnabled
        uiView.currentStyle = currentStyle

        // 监听清除触发器
        if context.coordinator.lastClearTrigger != clearTrigger {
            context.coordinator.lastClearTrigger = clearTrigger
            uiView.clearCanvas()
        }

        // 触发重绘以显示最新的线条
        uiView.setNeedsDisplay()
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    // MARK: - Coordinator
    class Coordinator: NSObject, DrawingCanvasDelegate {
        var parent: CanvasView
        var lastClearTrigger: Bool = false

        init(_ parent: CanvasView) {
            self.parent = parent
            self.lastClearTrigger = parent.clearTrigger
        }

        func strokeStarted(at point: CGPoint, pressure: Float) {
            parent.strokeEngine.startStroke(at: point, pressure: pressure, style: parent.currentStyle)
            parent.onStrokeStarted?(point, pressure)
        }

        func strokeUpdated(at point: CGPoint, pressure: Float) {
            parent.strokeEngine.addPoint(at: point, pressure: pressure)
            parent.onStrokeUpdated?(point, pressure)
        }

        func strokeEnded() {
            parent.strokeEngine.endStroke()

            // 同步线条数据到绑定
            DispatchQueue.main.async { [weak self] in
                guard let self = self else { return }
                self.parent.strokes = self.parent.dataManager.strokes
            }

            parent.onStrokeEnded?()
        }

        func strokesChanged(_ strokes: [Stroke]) {
            // 这个方法现在由StrokeEngine管理，不需要手动更新
        }
    }
}

// MARK: - 绘图画布委托协议
protocol DrawingCanvasDelegate: AnyObject {
    func strokeStarted(at point: CGPoint, pressure: Float)
    func strokeUpdated(at point: CGPoint, pressure: Float)
    func strokeEnded()
    func strokesChanged(_ strokes: [Stroke])
}

// MARK: - 自定义绘图画布
class DrawingCanvasView: UIView {
    // MARK: - 属性
    weak var delegate: DrawingCanvasDelegate?
    weak var strokeEngine: StrokeEngine?
    weak var renderEngine: RenderEngine?

    var isDrawingEnabled: Bool = true {
        didSet {
            isUserInteractionEnabled = isDrawingEnabled
        }
    }

    var currentStyle: StrokeStyle = .defaultPen

    // 性能监控
    private let performanceMonitor = PerformanceMonitor.shared
    private var inputStartTime: TimeInterval = 0

    // 局部重绘优化
    private var lastDrawPoint: CGPoint?

    // MARK: - 初始化
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }

    private func setupView() {
        backgroundColor = UIColor.systemBackground
        isMultipleTouchEnabled = false

        // 启用Apple Pencil支持
        if #available(iOS 12.9, *) {
            // 配置Apple Pencil设置
        }

        // 启动性能监控
        performanceMonitor.startMonitoring()
    }

    // MARK: - 公共方法
    func clearCanvas() {
        strokeEngine?.clearAllStrokes()
        setNeedsDisplay()
    }

    // MARK: - 绘制
    override func draw(_ rect: CGRect) {
        guard let context = UIGraphicsGetCurrentContext() else { return }

        performanceMonitor.startMeasuring("draw_frame")

        // 清空背景
        context.setFillColor(UIColor.systemBackground.cgColor)
        context.fill(rect)

        guard let engine = strokeEngine else {
            performanceMonitor.endMeasuring("draw_frame")
            return
        }

        // 使用RenderEngine渲染所有完成的线条
        if let renderEngine = renderEngine {
            renderEngine.renderStrokes(engine.completedStrokes, in: context, viewport: rect)
        } else {
            // 后备方案：直接渲染
            for stroke in engine.completedStrokes {
                drawStroke(stroke, in: context)
            }
        }

        // 绘制当前正在绘制的线条 - 使用整体渲染避免断续
        if let currentStroke = engine.currentStroke {
            // 正在绘制的线条直接使用StrokeRenderer，不使用分段
            StrokeRenderer.renderStroke(currentStroke, in: context, viewBounds: bounds)
        }

        performanceMonitor.endMeasuring("draw_frame")
    }

    private func drawStroke(_ stroke: Stroke, in context: CGContext) {
        guard !stroke.points.isEmpty else { return }

        // 只绘制未擦除的分段
        let activeSegments = stroke.activeSegments

        for segment in activeSegments {
            drawSegment(segment, of: stroke, in: context)
        }
    }

    private func drawSegment(_ segment: StrokeSegment, of stroke: Stroke, in context: CGContext) {
        guard segment.isValid && segment.startIndex < stroke.points.count && segment.endIndex < stroke.points.count else {
            return
        }

        let points = Array(stroke.points[segment.startIndex...segment.endIndex])
        guard !points.isEmpty else { return }

        // 创建临时线条对象用于渲染
        let tempStroke = Stroke(style: stroke.style)
        for point in points {
            tempStroke.addPoint(point)
        }

        // 使用新的渲染器
        StrokeRenderer.renderStroke(tempStroke, in: context, viewBounds: bounds)
    }

    private func drawPoint(_ point: StrokePoint, style: StrokeStyle, in context: CGContext) {
        let width = style.calculateWidth(for: point.pressure)
        let colorComponents = style.colorComponents

        context.setFillColor(red: CGFloat(colorComponents.r),
                            green: CGFloat(colorComponents.g),
                            blue: CGFloat(colorComponents.b),
                            alpha: CGFloat(colorComponents.a * style.opacity))

        let rect = CGRect(
            x: CGFloat(point.x) - CGFloat(width) / 2,
            y: CGFloat(point.y) - CGFloat(width) / 2,
            width: CGFloat(width),
            height: CGFloat(width)
        )

        context.fillEllipse(in: rect)
    }
}

// MARK: - 触摸处理
extension DrawingCanvasView {
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard isDrawingEnabled, let touch = touches.first else { return }

        inputStartTime = CACurrentMediaTime()

        let location = touch.location(in: self)
        let pressure = getPressure(from: touch)

        // 记录起始点用于局部重绘
        lastDrawPoint = location

        delegate?.strokeStarted(at: location, pressure: pressure)
        setNeedsDisplay()

        // 记录输入延迟
        let inputLatency = CACurrentMediaTime() - inputStartTime
        performanceMonitor.recordInputLatency(inputLatency)
    }

    override func touchesMoved(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard isDrawingEnabled, let touch = touches.first else { return }

        let location = touch.location(in: self)
        let pressure = getPressure(from: touch)

        delegate?.strokeUpdated(at: location, pressure: pressure)

        // 智能重绘：如果是当前正在绘制的线条，使用局部重绘
        if let engine = strokeEngine, engine.isProcessing {
            setNeedsDisplayInRegion(for: location, pressure: pressure)
        } else {
            // 其他情况使用全局重绘
            setNeedsDisplay()
        }
    }

    override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard isDrawingEnabled else { return }

        delegate?.strokeEnded()
        lastDrawPoint = nil  // 清除记录的点
        setNeedsDisplay()
    }

    override func touchesCancelled(_ touches: Set<UITouch>, with event: UIEvent?) {
        touchesEnded(touches, with: event)
    }

    private func getPressure(from touch: UITouch) -> Float {
        if touch.type == .pencil {
            // Apple Pencil 支持压感
            return Float(touch.force / touch.maximumPossibleForce)
        } else {
            // 手指触摸，使用固定压力值
            return 0.5
        }
    }

    /// 智能局部重绘 - 修复虚线问题
    private func setNeedsDisplayInRegion(for currentPoint: CGPoint, pressure: Float) {
        // 计算考虑压感的实际线宽
        let actualWidth = currentStyle.calculateWidth(for: pressure)

        // 计算重绘区域
        let redrawRect: CGRect

        if let lastPoint = lastDrawPoint {
            // 计算从上一个点到当前点的连接区域
            let minX = min(lastPoint.x, currentPoint.x)
            let maxX = max(lastPoint.x, currentPoint.x)
            let minY = min(lastPoint.y, currentPoint.y)
            let maxY = max(lastPoint.y, currentPoint.y)

            // 足够的边距：考虑线宽、抗锯齿、压感变化
            let margin = CGFloat(actualWidth * 2 + 10) // 更大的边距确保完整重绘

            redrawRect = CGRect(
                x: minX - margin,
                y: minY - margin,
                width: maxX - minX + margin * 2,
                height: maxY - minY + margin * 2
            )
        } else {
            // 没有上一个点，重绘当前点周围区域
            let margin = CGFloat(actualWidth * 2 + 10)
            redrawRect = CGRect(
                x: currentPoint.x - margin,
                y: currentPoint.y - margin,
                width: margin * 2,
                height: margin * 2
            )
        }

        // 确保重绘区域在视图范围内
        let clampedRect = redrawRect.intersection(bounds)
        if !clampedRect.isEmpty {
            setNeedsDisplay(clampedRect)
        }

        // 更新上一个点
        lastDrawPoint = currentPoint
    }
}

// MARK: - 预览支持
struct CanvasView_Previews: PreviewProvider {
    @State static var strokes: [Stroke] = []
    @State static var currentStyle = StrokeStyle.defaultPen
    @State static var isDrawingEnabled = true
    @State static var clearTrigger = false

    static var previews: some View {
        CanvasView(
            strokes: $strokes,
            currentStyle: $currentStyle,
            isDrawingEnabled: $isDrawingEnabled,
            clearTrigger: $clearTrigger
        )
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.white)
    }
}
