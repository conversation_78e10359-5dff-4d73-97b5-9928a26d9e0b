//
//  ContentView.swift
//  path_generator
//
//  Created by 杨冰冰 on 2025/5/26.
//

import SwiftUI

struct ContentView: View {
    // MARK: - 状态管理
    @State private var strokes: [Stroke] = []
    @State private var currentStyle: StrokeStyle = .defaultPen
    @State private var isDrawingEnabled: Bool = true
    @State private var showingStylePicker: Bool = false


    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 工具栏
                toolBar

                // 主画布
                CanvasView(
                    strokes: $strokes,
                    currentStyle: $currentStyle,
                    isDrawingEnabled: $isDrawingEnabled,
                    onStrokeStarted: { point, pressure in
                        print("Stroke started at \(point) with pressure \(pressure)")
                    },
                    onStrokeUpdated: { point, pressure in
                        // 可以在这里添加实时反馈
                    },
                    onStrokeEnded: {
                        print("Stroke ended. Total strokes: \(strokes.count)")
                    }
                )
                .background(Color.white)
                .clipped()
            }
            .navigationTitle("线条构造器")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("清空") {
                        clearCanvas()
                    }
                    .disabled(strokes.isEmpty)
                }
            }
        }
        .sheet(isPresented: $showingStylePicker) {
            StylePickerView(selectedStyle: $currentStyle)
        }
    }

    // MARK: - 工具栏
    private var toolBar: some View {
        HStack {
            // 笔刷选择按钮
            Button(action: {
                showingStylePicker = true
            }) {
                HStack {
                    Circle()
                        .fill(currentStyle.color)
                        .frame(width: 20, height: 20)
                    Text(currentStyle.brushType.displayName)
                        .font(.caption)
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color.gray.opacity(0.2))
                .cornerRadius(8)
            }

            Spacer()

            // 绘制状态指示器
            HStack {
                Circle()
                    .fill(isDrawingEnabled ? Color.green : Color.red)
                    .frame(width: 8, height: 8)
                Text(isDrawingEnabled ? "绘制模式" : "查看模式")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            // 统计信息 - 使用独立的性能监控视图
            VStack(alignment: .trailing) {
                Text("线条: \(strokes.count)")
                    .font(.caption)
                    .foregroundColor(.secondary)
                if !strokes.isEmpty {
                    let totalPoints = strokes.reduce(0) { $0 + $1.pointCount }
                    Text("点数: \(totalPoints)")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                PerformanceDisplayView()
            }
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        .background(Color.gray.opacity(0.1))
    }

    // MARK: - 操作方法
    private func clearCanvas() {
        strokes.removeAll()
    }
}

// MARK: - 样式选择器视图
struct StylePickerView: View {
    @Binding var selectedStyle: StrokeStyle
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            List {
                Section("预设笔刷") {
                    ForEach(StrokeStyle.allPresets.indices, id: \.self) { index in
                        let preset = StrokeStyle.allPresets[index]
                        StyleRowView(style: preset, isSelected: selectedStyle.brushType == preset.brushType) {
                            selectedStyle = preset
                        }
                    }
                }

                Section("自定义设置") {
                    VStack(alignment: .leading, spacing: 12) {
                        // 线条宽度
                        VStack(alignment: .leading) {
                            Text("线条宽度: \(selectedStyle.baseWidth, specifier: "%.1f")")
                                .font(.caption)
                            Slider(value: $selectedStyle.baseWidth, in: 1...20, step: 0.5)
                        }

                        // 压感敏感度
                        VStack(alignment: .leading) {
                            Text("压感敏感度: \(selectedStyle.pressureSensitivity, specifier: "%.1f")")
                                .font(.caption)
                            Slider(value: $selectedStyle.pressureSensitivity, in: 0.1...2.0, step: 0.1)
                        }

                        // 不透明度
                        VStack(alignment: .leading) {
                            Text("不透明度: \(selectedStyle.opacity, specifier: "%.1f")")
                                .font(.caption)
                            Slider(value: $selectedStyle.opacity, in: 0.1...1.0, step: 0.1)
                        }
                    }
                    .padding(.vertical, 8)
                }
            }
            .navigationTitle("选择笔刷")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - 样式行视图
struct StyleRowView: View {
    let style: StrokeStyle
    let isSelected: Bool
    let onTap: () -> Void

    var body: some View {
        HStack {
            // 笔刷预览
            HStack {
                Circle()
                    .fill(style.color)
                    .frame(width: 16, height: 16)

                // 线条预览
                RoundedRectangle(cornerRadius: CGFloat(style.baseWidth) / 2)
                    .fill(style.color.opacity(Double(style.opacity)))
                    .frame(width: 40, height: CGFloat(style.baseWidth))
            }

            VStack(alignment: .leading) {
                Text(style.brushType.displayName)
                    .font(.body)
                Text("宽度: \(style.baseWidth, specifier: "%.1f"), 压感: \(style.pressureSensitivity, specifier: "%.1f")")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            if isSelected {
                Image(systemName: "checkmark")
                    .foregroundColor(.blue)
            }
        }
        .contentShape(Rectangle())
        .onTapGesture {
            onTap()
        }
    }
}

// MARK: - 性能监控显示视图
struct PerformanceDisplayView: View {
    @StateObject private var performanceMonitor = PerformanceMonitor.shared

    var body: some View {
        Text("FPS: \(Int(performanceMonitor.currentFPS))")
            .font(.caption2)
            .foregroundColor(.secondary)
    }
}

#Preview {
    ContentView()
}
