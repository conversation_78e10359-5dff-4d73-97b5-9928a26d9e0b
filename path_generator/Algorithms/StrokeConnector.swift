//
//  StrokeConnector.swift
//  path_generator
//
//  Created by AI Assistant on 2025/5/27.
//

import Foundation
import UIKit

/// 线条连接算法 - 负责智能连接和合并线条
class StrokeConnector {

    // MARK: - 配置参数
    private struct ConnectorConfig {
        static let maxConnectionDistance: Float = 20.0  // 最大连接距离
        static let maxAngleDifference: Float = 30.0     // 最大角度差异（度）
        static let minOverlapLength: Float = 10.0       // 最小重叠长度
        static let connectionTolerance: Float = 5.0     // 连接容差
    }

    // MARK: - 连接结果
    struct ConnectionResult {
        let success: Bool
        let connectedStroke: Stroke?
        let connectionType: ConnectionType
        let connectionPoint: StrokePoint?

        enum ConnectionType {
            case endToStart      // 尾部连接到头部
            case endToEnd        // 尾部连接到尾部
            case startToStart    // 头部连接到头部
            case startToEnd      // 头部连接到尾部
            case none            // 无连接
        }
    }

    // MARK: - 公共方法

    /// 尝试连接两个线条
    /// - Parameters:
    ///   - stroke1: 第一个线条
    ///   - stroke2: 第二个线条
    /// - Returns: 连接结果
    static func connectStrokes(_ stroke1: Stroke, _ stroke2: Stroke) -> ConnectionResult {
        guard canConnect(stroke1, stroke2) else {
            return ConnectionResult(success: false, connectedStroke: nil, connectionType: .none, connectionPoint: nil)
        }

        // 检查所有可能的连接方式
        let connections = [
            checkEndToStartConnection(stroke1, stroke2),
            checkEndToEndConnection(stroke1, stroke2),
            checkStartToStartConnection(stroke1, stroke2),
            checkStartToEndConnection(stroke1, stroke2)
        ]

        // 选择最佳连接
        if let bestConnection = connections.compactMap({ $0 }).min(by: { $0.distance < $1.distance }) {
            let connectedStroke = performConnection(stroke1, stroke2, using: bestConnection)
            return ConnectionResult(
                success: true,
                connectedStroke: connectedStroke,
                connectionType: bestConnection.type,
                connectionPoint: bestConnection.connectionPoint
            )
        }

        return ConnectionResult(success: false, connectedStroke: nil, connectionType: .none, connectionPoint: nil)
    }

    /// 自动连接线条数组中的相邻线条
    /// - Parameter strokes: 线条数组
    /// - Returns: 连接后的线条数组
    static func autoConnectStrokes(_ strokes: [Stroke]) -> [Stroke] {
        var result = strokes
        var hasConnections = true

        while hasConnections {
            hasConnections = false

            for i in 0..<result.count {
                for j in (i+1)..<result.count {
                    let connectionResult = connectStrokes(result[i], result[j])

                    if connectionResult.success, let connectedStroke = connectionResult.connectedStroke {
                        // 替换原有线条
                        result.remove(at: j)
                        result.remove(at: i)
                        result.insert(connectedStroke, at: i)
                        hasConnections = true
                        break
                    }
                }
                if hasConnections { break }
            }
        }

        return result
    }

    /// 查找可以连接到指定线条的候选线条
    /// - Parameters:
    ///   - targetStroke: 目标线条
    ///   - candidates: 候选线条数组
    /// - Returns: 可连接的线条数组
    static func findConnectableCandidates(for targetStroke: Stroke, in candidates: [Stroke]) -> [Stroke] {
        return candidates.filter { candidate in
            candidate !== targetStroke && canConnect(targetStroke, candidate)
        }
    }

    // MARK: - 私有方法

    /// 检查两个线条是否可以连接
    private static func canConnect(_ stroke1: Stroke, _ stroke2: Stroke) -> Bool {
        // 检查样式兼容性
        guard areStylesCompatible(stroke1.style, stroke2.style) else { return false }

        // 检查时间间隔（如果线条绘制时间相近，更可能需要连接）
        // 使用线条中第一个点的时间戳作为线条时间戳
        let time1 = stroke1.points.first?.timestamp ?? 0
        let time2 = stroke2.points.first?.timestamp ?? 0
        let timeDifference = abs(time1 - time2)
        guard timeDifference < 5.0 else { return false } // 5秒内的线条

        return true
    }

    /// 检查样式是否兼容
    private static func areStylesCompatible(_ style1: StrokeStyle, _ style2: StrokeStyle) -> Bool {
        // 相同的笔刷类型
        guard style1.brushType == style2.brushType else { return false }

        // 相似的颜色
        let color1 = style1.colorComponents
        let color2 = style2.colorComponents
        let colorDifference = sqrt(
            pow(color1.r - color2.r, 2) +
            pow(color1.g - color2.g, 2) +
            pow(color1.b - color2.b, 2)
        )
        guard colorDifference < 0.1 else { return false }

        // 相似的线宽
        let widthDifference = abs(style1.baseWidth - style2.baseWidth)
        guard widthDifference < style1.baseWidth * 0.3 else { return false }

        return true
    }

    // MARK: - 连接检查方法

    private struct ConnectionCandidate {
        let type: ConnectionResult.ConnectionType
        let distance: Float
        let connectionPoint: StrokePoint
        let angle: Float
    }

    /// 检查尾部到头部连接
    private static func checkEndToStartConnection(_ stroke1: Stroke, _ stroke2: Stroke) -> ConnectionCandidate? {
        guard let end1 = stroke1.points.last, let start2 = stroke2.points.first else { return nil }

        let distance = end1.distance(to: start2)
        guard distance <= ConnectorConfig.maxConnectionDistance else { return nil }

        let angle = calculateConnectionAngle(stroke1, stroke2, .endToStart)
        guard angle <= ConnectorConfig.maxAngleDifference else { return nil }

        let connectionPoint = interpolateConnectionPoint(end1, start2)

        return ConnectionCandidate(
            type: .endToStart,
            distance: distance,
            connectionPoint: connectionPoint,
            angle: angle
        )
    }

    /// 检查尾部到尾部连接
    private static func checkEndToEndConnection(_ stroke1: Stroke, _ stroke2: Stroke) -> ConnectionCandidate? {
        guard let end1 = stroke1.points.last, let end2 = stroke2.points.last else { return nil }

        let distance = end1.distance(to: end2)
        guard distance <= ConnectorConfig.maxConnectionDistance else { return nil }

        let angle = calculateConnectionAngle(stroke1, stroke2, .endToEnd)
        guard angle <= ConnectorConfig.maxAngleDifference else { return nil }

        let connectionPoint = interpolateConnectionPoint(end1, end2)

        return ConnectionCandidate(
            type: .endToEnd,
            distance: distance,
            connectionPoint: connectionPoint,
            angle: angle
        )
    }

    /// 检查头部到头部连接
    private static func checkStartToStartConnection(_ stroke1: Stroke, _ stroke2: Stroke) -> ConnectionCandidate? {
        guard let start1 = stroke1.points.first, let start2 = stroke2.points.first else { return nil }

        let distance = start1.distance(to: start2)
        guard distance <= ConnectorConfig.maxConnectionDistance else { return nil }

        let angle = calculateConnectionAngle(stroke1, stroke2, .startToStart)
        guard angle <= ConnectorConfig.maxAngleDifference else { return nil }

        let connectionPoint = interpolateConnectionPoint(start1, start2)

        return ConnectionCandidate(
            type: .startToStart,
            distance: distance,
            connectionPoint: connectionPoint,
            angle: angle
        )
    }

    /// 检查头部到尾部连接
    private static func checkStartToEndConnection(_ stroke1: Stroke, _ stroke2: Stroke) -> ConnectionCandidate? {
        guard let start1 = stroke1.points.first, let end2 = stroke2.points.last else { return nil }

        let distance = start1.distance(to: end2)
        guard distance <= ConnectorConfig.maxConnectionDistance else { return nil }

        let angle = calculateConnectionAngle(stroke1, stroke2, .startToEnd)
        guard angle <= ConnectorConfig.maxAngleDifference else { return nil }

        let connectionPoint = interpolateConnectionPoint(start1, end2)

        return ConnectionCandidate(
            type: .startToEnd,
            distance: distance,
            connectionPoint: connectionPoint,
            angle: angle
        )
    }

    /// 计算连接角度
    private static func calculateConnectionAngle(_ stroke1: Stroke, _ stroke2: Stroke, _ type: ConnectionResult.ConnectionType) -> Float {
        // 获取线条末端的方向向量
        let direction1 = getStrokeDirection(stroke1, atEnd: type == .endToStart || type == .endToEnd)
        let direction2 = getStrokeDirection(stroke2, atEnd: type == .endToEnd || type == .startToEnd)

        // 计算角度差异
        let dotProduct = direction1.x * direction2.x + direction1.y * direction2.y
        let magnitude1 = sqrt(direction1.x * direction1.x + direction1.y * direction1.y)
        let magnitude2 = sqrt(direction2.x * direction2.x + direction2.y * direction2.y)

        guard magnitude1 > 0 && magnitude2 > 0 else { return 180.0 }

        let cosAngle = dotProduct / (magnitude1 * magnitude2)
        let angle = acos(max(-1.0, min(1.0, cosAngle))) * 180.0 / Float.pi

        return min(angle, 180.0 - angle) // 返回较小的角度
    }

    /// 获取线条方向
    private static func getStrokeDirection(_ stroke: Stroke, atEnd: Bool) -> (x: Float, y: Float) {
        let points = stroke.points
        guard points.count >= 2 else { return (0, 0) }

        if atEnd {
            let lastPoint = points[points.count - 1]
            let secondLastPoint = points[points.count - 2]
            return (lastPoint.x - secondLastPoint.x, lastPoint.y - secondLastPoint.y)
        } else {
            let firstPoint = points[0]
            let secondPoint = points[1]
            return (secondPoint.x - firstPoint.x, secondPoint.y - firstPoint.y)
        }
    }

    /// 插值连接点
    private static func interpolateConnectionPoint(_ point1: StrokePoint, _ point2: StrokePoint) -> StrokePoint {
        let midX = (point1.x + point2.x) / 2
        let midY = (point1.y + point2.y) / 2
        let avgPressure = (point1.pressure + point2.pressure) / 2
        let avgTimestamp = (point1.timestamp + point2.timestamp) / 2

        return StrokePoint(x: midX, y: midY, pressure: avgPressure, timestamp: avgTimestamp)
    }

    /// 执行连接
    private static func performConnection(_ stroke1: Stroke, _ stroke2: Stroke, using candidate: ConnectionCandidate) -> Stroke {
        let connectedStroke = Stroke(style: stroke1.style)

        switch candidate.type {
        case .endToStart:
            // stroke1 + connectionPoint + stroke2
            for point in stroke1.points {
                connectedStroke.addPoint(point)
            }
            connectedStroke.addPoint(candidate.connectionPoint)
            for point in stroke2.points {
                connectedStroke.addPoint(point)
            }

        case .endToEnd:
            // stroke1 + connectionPoint + reversed stroke2
            for point in stroke1.points {
                connectedStroke.addPoint(point)
            }
            connectedStroke.addPoint(candidate.connectionPoint)
            for point in stroke2.points.reversed() {
                connectedStroke.addPoint(point)
            }

        case .startToStart:
            // reversed stroke1 + connectionPoint + stroke2
            for point in stroke1.points.reversed() {
                connectedStroke.addPoint(point)
            }
            connectedStroke.addPoint(candidate.connectionPoint)
            for point in stroke2.points {
                connectedStroke.addPoint(point)
            }

        case .startToEnd:
            // stroke2 + connectionPoint + stroke1
            for point in stroke2.points {
                connectedStroke.addPoint(point)
            }
            connectedStroke.addPoint(candidate.connectionPoint)
            for point in stroke1.points {
                connectedStroke.addPoint(point)
            }

        case .none:
            break
        }

        connectedStroke.complete()
        return connectedStroke
    }
}
