//
//  PointOptimizer.swift
//  path_generator
//
//  Created by AI Assistant on 2025/5/27.
//

import Foundation
import simd

/// 高级点优化算法集合
/// 提供多种点简化和优化策略，平衡性能和质量
class PointOptimizer {
    
    // MARK: - 配置参数
    struct OptimizationConfig {
        // Douglas-Peucker算法参数
        var dpTolerance: Float = 1.0
        var dpMinPoints: Int = 3
        
        // 曲率感知参数
        var curvatureThreshold: Float = 0.1
        var curvatureWindow: Int = 3
        
        // 自适应采样参数
        var speedThreshold: Float = 100.0  // 像素/秒
        var pressureThreshold: Float = 0.1
        
        // 质量控制参数
        var maxCompressionRatio: Float = 0.3  // 最大压缩比
        var minQualityScore: Float = 0.95     // 最小质量分数
    }
    
    private let config: OptimizationConfig
    
    init(config: OptimizationConfig = OptimizationConfig()) {
        self.config = config
    }
    
    // MARK: - 主要优化方法
    
    /// 综合优化点序列
    /// - Parameters:
    ///   - points: 原始点序列
    ///   - strategy: 优化策略
    /// - Returns: 优化后的点序列
    func optimizePoints(_ points: [StrokePoint], strategy: OptimizationStrategy = .balanced) -> [StrokePoint] {
        guard points.count > config.dpMinPoints else { return points }
        
        let originalCount = points.count
        var optimizedPoints = points
        
        switch strategy {
        case .aggressive:
            // 激进优化：最大压缩
            optimizedPoints = douglasPeucker(optimizedPoints, tolerance: config.dpTolerance * 2.0)
            optimizedPoints = removeRedundantPoints(optimizedPoints, threshold: 2.0)
            
        case .balanced:
            // 平衡优化：质量和性能并重
            optimizedPoints = curvatureAwareOptimization(optimizedPoints)
            optimizedPoints = douglasPeucker(optimizedPoints, tolerance: config.dpTolerance)
            
        case .conservative:
            // 保守优化：优先保证质量
            optimizedPoints = removeRedundantPoints(optimizedPoints, threshold: 0.5)
            optimizedPoints = pressureAwareOptimization(optimizedPoints)
            
        case .adaptive:
            // 自适应优化：根据内容特征选择策略
            optimizedPoints = adaptiveOptimization(optimizedPoints)
        }
        
        // 质量检查
        let compressionRatio = Float(optimizedPoints.count) / Float(originalCount)
        if compressionRatio < config.maxCompressionRatio {
            // 压缩过度，使用保守策略
            optimizedPoints = removeRedundantPoints(points, threshold: 1.0)
        }
        
        print("🔧 Point optimization: \(originalCount) → \(optimizedPoints.count) points (ratio: \(String(format: "%.2f", compressionRatio)))")
        
        return optimizedPoints
    }
    
    // MARK: - Douglas-Peucker算法
    
    /// Douglas-Peucker线简化算法
    /// - Parameters:
    ///   - points: 点序列
    ///   - tolerance: 容差值
    /// - Returns: 简化后的点序列
    func douglasPeucker(_ points: [StrokePoint], tolerance: Float) -> [StrokePoint] {
        guard points.count > 2 else { return points }
        
        let (maxDistance, maxIndex) = findFarthestPoint(points, tolerance: tolerance)
        
        if maxDistance > tolerance {
            // 递归处理两段
            let leftPoints = Array(points[0...maxIndex])
            let rightPoints = Array(points[maxIndex...])
            
            let leftResult = douglasPeucker(leftPoints, tolerance: tolerance)
            let rightResult = douglasPeucker(rightPoints, tolerance: tolerance)
            
            // 合并结果，去除重复的中间点
            return leftResult + Array(rightResult.dropFirst())
        } else {
            // 只保留首尾点
            return [points.first!, points.last!]
        }
    }
    
    /// 找到距离直线最远的点
    private func findFarthestPoint(_ points: [StrokePoint], tolerance: Float) -> (distance: Float, index: Int) {
        guard points.count > 2 else { return (0, 0) }
        
        let start = points.first!
        let end = points.last!
        
        var maxDistance: Float = 0
        var maxIndex = 0
        
        for i in 1..<(points.count - 1) {
            let distance = perpendicularDistance(points[i], lineStart: start, lineEnd: end)
            if distance > maxDistance {
                maxDistance = distance
                maxIndex = i
            }
        }
        
        return (maxDistance, maxIndex)
    }
    
    /// 计算点到直线的垂直距离
    private func perpendicularDistance(_ point: StrokePoint, lineStart: StrokePoint, lineEnd: StrokePoint) -> Float {
        let A = lineEnd.y - lineStart.y
        let B = lineStart.x - lineEnd.x
        let C = lineEnd.x * lineStart.y - lineStart.x * lineEnd.y
        
        let numerator = abs(A * point.x + B * point.y + C)
        let denominator = sqrt(A * A + B * B)
        
        return denominator > 0 ? numerator / denominator : 0
    }
    
    // MARK: - 曲率感知优化
    
    /// 基于曲率的智能优化
    private func curvatureAwareOptimization(_ points: [StrokePoint]) -> [StrokePoint] {
        guard points.count > config.curvatureWindow * 2 else { return points }
        
        var optimizedPoints: [StrokePoint] = []
        optimizedPoints.append(points[0]) // 保留第一个点
        
        for i in 1..<(points.count - 1) {
            let curvature = calculateCurvature(at: i, in: points)
            
            // 高曲率点必须保留
            if curvature > config.curvatureThreshold {
                optimizedPoints.append(points[i])
            } else {
                // 低曲率区域可以跳过一些点
                let lastAdded = optimizedPoints.last!
                let distance = points[i].distance(to: lastAdded)
                
                // 距离足够远时才添加
                if distance > 2.0 {
                    optimizedPoints.append(points[i])
                }
            }
        }
        
        optimizedPoints.append(points.last!) // 保留最后一个点
        return optimizedPoints
    }
    
    /// 计算指定点的曲率
    private func calculateCurvature(at index: Int, in points: [StrokePoint]) -> Float {
        let window = config.curvatureWindow
        let start = max(0, index - window)
        let end = min(points.count - 1, index + window)
        
        guard end - start >= 2 else { return 0 }
        
        let p1 = points[start]
        let p2 = points[index]
        let p3 = points[end]
        
        // 使用三点计算曲率
        let area = triangleArea(p1, p2, p3)
        let a = p1.distance(to: p2)
        let b = p2.distance(to: p3)
        let c = p1.distance(to: p3)
        
        guard a > 0 && b > 0 && c > 0 else { return 0 }
        
        return (4 * area) / (a * b * c)
    }
    
    /// 计算三角形面积
    private func triangleArea(_ p1: StrokePoint, _ p2: StrokePoint, _ p3: StrokePoint) -> Float {
        return abs((p1.x * (p2.y - p3.y) + p2.x * (p3.y - p1.y) + p3.x * (p1.y - p2.y)) / 2.0)
    }
    
    // MARK: - 其他优化策略
    
    /// 移除冗余点
    private func removeRedundantPoints(_ points: [StrokePoint], threshold: Float) -> [StrokePoint] {
        guard points.count > 2 else { return points }
        
        var optimizedPoints: [StrokePoint] = []
        optimizedPoints.append(points[0])
        
        for i in 1..<points.count {
            let lastAdded = optimizedPoints.last!
            let current = points[i]
            
            let distance = current.distance(to: lastAdded)
            let pressureDiff = abs(current.pressure - lastAdded.pressure)
            
            // 距离足够远或压力变化显著时保留点
            if distance >= threshold || pressureDiff > config.pressureThreshold {
                optimizedPoints.append(current)
            }
        }
        
        return optimizedPoints
    }
    
    /// 压力感知优化
    private func pressureAwareOptimization(_ points: [StrokePoint]) -> [StrokePoint] {
        // 在压力变化显著的地方保留更多点
        var optimizedPoints: [StrokePoint] = []
        optimizedPoints.append(points[0])
        
        for i in 1..<points.count {
            let lastAdded = optimizedPoints.last!
            let current = points[i]
            
            let pressureDiff = abs(current.pressure - lastAdded.pressure)
            let distance = current.distance(to: lastAdded)
            
            // 压力变化大的区域使用更小的距离阈值
            let threshold: Float = pressureDiff > config.pressureThreshold ? 0.5 : 1.5
            
            if distance >= threshold {
                optimizedPoints.append(current)
            }
        }
        
        return optimizedPoints
    }
    
    /// 自适应优化
    private func adaptiveOptimization(_ points: [StrokePoint]) -> [StrokePoint] {
        // 分析点序列特征
        let characteristics = analyzePointCharacteristics(points)
        
        if characteristics.isHighSpeed {
            // 高速绘制：使用激进优化
            return douglasPeucker(points, tolerance: config.dpTolerance * 1.5)
        } else if characteristics.isHighDetail {
            // 高细节：使用保守优化
            return removeRedundantPoints(points, threshold: 0.5)
        } else {
            // 普通情况：使用平衡优化
            return curvatureAwareOptimization(points)
        }
    }
    
    /// 分析点序列特征
    private func analyzePointCharacteristics(_ points: [StrokePoint]) -> PointCharacteristics {
        guard points.count > 1 else {
            return PointCharacteristics(isHighSpeed: false, isHighDetail: false)
        }
        
        // 计算平均速度
        var totalDistance: Float = 0
        var totalTime: TimeInterval = 0
        
        for i in 1..<points.count {
            totalDistance += points[i].distance(to: points[i-1])
            totalTime += points[i].timestamp - points[i-1].timestamp
        }
        
        let averageSpeed = totalTime > 0 ? Float(Double(totalDistance) / totalTime) : 0
        
        // 计算曲率变化
        var curvatureVariance: Float = 0
        if points.count > 4 {
            var curvatures: [Float] = []
            for i in 2..<(points.count - 2) {
                curvatures.append(calculateCurvature(at: i, in: points))
            }
            
            let meanCurvature = curvatures.reduce(0, +) / Float(curvatures.count)
            curvatureVariance = curvatures.map { pow($0 - meanCurvature, 2) }.reduce(0, +) / Float(curvatures.count)
        }
        
        return PointCharacteristics(
            isHighSpeed: averageSpeed > config.speedThreshold,
            isHighDetail: curvatureVariance > 0.05
        )
    }
}

// MARK: - 支持类型

enum OptimizationStrategy {
    case aggressive    // 激进优化，最大压缩
    case balanced      // 平衡优化，质量和性能并重
    case conservative  // 保守优化，优先保证质量
    case adaptive      // 自适应优化，根据内容特征选择
}

struct PointCharacteristics {
    let isHighSpeed: Bool    // 是否高速绘制
    let isHighDetail: Bool   // 是否高细节内容
}
