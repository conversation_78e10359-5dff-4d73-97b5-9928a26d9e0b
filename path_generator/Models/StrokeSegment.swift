//
//  StrokeSegment.swift
//  path_generator
//
//  Created by AI Assistant on 2025/5/26.
//

import Foundation
import CoreGraphics

/// 线条分段 - 用于支持部分擦除功能
/// 将长线条分解为多个小段，便于精确擦除
struct StrokeSegment: Identifiable, Codable, Equatable {
    // MARK: - 基础属性
    /// 唯一标识符
    let id = UUID()
    
    /// 在父线条中的起始点索引
    var startIndex: Int
    
    /// 在父线条中的结束点索引
    var endIndex: Int
    
    /// 是否已被擦除
    var isErased: Bool
    
    /// 分段的边界框 (用于快速碰撞检测)
    private var _boundingBox: CGRect?
    
    // MARK: - 计算属性
    /// 分段包含的点数量
    var pointCount: Int {
        return max(0, endIndex - startIndex + 1)
    }
    
    /// 分段是否有效
    var isValid: Bool {
        return startIndex <= endIndex && pointCount > 0
    }
    
    /// 分段长度 (点的索引范围)
    var length: Int {
        return endIndex - startIndex
    }
    
    // MARK: - 初始化器
    /// 创建新的线条分段
    /// - Parameters:
    ///   - startIndex: 起始点索引
    ///   - endIndex: 结束点索引
    ///   - isErased: 是否已擦除
    init(startIndex: Int, endIndex: Int, isErased: Bool = false) {
        self.startIndex = startIndex
        self.endIndex = endIndex
        self.isErased = isErased
    }
    
    /// 便捷初始化器 - 指定起始点和长度
    /// - Parameters:
    ///   - startIndex: 起始点索引
    ///   - length: 分段长度
    ///   - isErased: 是否已擦除
    init(startIndex: Int, length: Int, isErased: Bool = false) {
        self.startIndex = startIndex
        self.endIndex = startIndex + length - 1
        self.isErased = isErased
    }
}

// MARK: - 几何计算扩展
extension StrokeSegment {
    /// 计算分段的边界框
    /// - Parameter points: 父线条的所有点
    /// - Returns: 边界框
    func calculateBoundingBox(from points: [StrokePoint]) -> CGRect {
        guard isValid && startIndex < points.count && endIndex < points.count else {
            return .zero
        }
        
        let segmentPoints = Array(points[startIndex...endIndex])
        guard !segmentPoints.isEmpty else { return .zero }
        
        let xs = segmentPoints.map { CGFloat($0.x) }
        let ys = segmentPoints.map { CGFloat($0.y) }
        
        let minX = xs.min()!
        let maxX = xs.max()!
        let minY = ys.min()!
        let maxY = ys.max()!
        
        return CGRect(
            x: minX,
            y: minY,
            width: maxX - minX,
            height: maxY - minY
        )
    }
    
    /// 获取缓存的边界框
    /// - Parameter points: 父线条的所有点
    /// - Returns: 边界框
    mutating func getBoundingBox(from points: [StrokePoint]) -> CGRect {
        if _boundingBox == nil {
            _boundingBox = calculateBoundingBox(from: points)
        }
        return _boundingBox!
    }
    
    /// 使边界框缓存失效
    mutating func invalidateBoundingBox() {
        _boundingBox = nil
    }
    
    /// 检查分段是否与矩形相交
    /// - Parameters:
    ///   - rect: 目标矩形
    ///   - points: 父线条的所有点
    /// - Returns: 是否相交
    mutating func intersects(with rect: CGRect, points: [StrokePoint]) -> Bool {
        let boundingBox = getBoundingBox(from: points)
        return boundingBox.intersects(rect)
    }
    
    /// 检查分段是否包含指定点
    /// - Parameters:
    ///   - point: 目标点
    ///   - points: 父线条的所有点
    ///   - tolerance: 容差距离
    /// - Returns: 是否包含
    func contains(point: CGPoint, in points: [StrokePoint], tolerance: CGFloat = 5.0) -> Bool {
        guard isValid && startIndex < points.count && endIndex < points.count else {
            return false
        }
        
        let segmentPoints = Array(points[startIndex...endIndex])
        
        // 检查点是否在任意两个相邻点构成的线段附近
        for i in 0..<(segmentPoints.count - 1) {
            let p1 = segmentPoints[i].cgPoint
            let p2 = segmentPoints[i + 1].cgPoint
            
            if distanceFromPointToLineSegment(point: point, lineStart: p1, lineEnd: p2) <= tolerance {
                return true
            }
        }
        
        return false
    }
    
    /// 计算点到线段的距离
    private func distanceFromPointToLineSegment(point: CGPoint, lineStart: CGPoint, lineEnd: CGPoint) -> CGFloat {
        let A = point.x - lineStart.x
        let B = point.y - lineStart.y
        let C = lineEnd.x - lineStart.x
        let D = lineEnd.y - lineStart.y
        
        let dot = A * C + B * D
        let lenSq = C * C + D * D
        
        if lenSq == 0 {
            // 线段退化为点
            return sqrt(A * A + B * B)
        }
        
        let param = dot / lenSq
        
        let closestPoint: CGPoint
        if param < 0 {
            closestPoint = lineStart
        } else if param > 1 {
            closestPoint = lineEnd
        } else {
            closestPoint = CGPoint(
                x: lineStart.x + param * C,
                y: lineStart.y + param * D
            )
        }
        
        let dx = point.x - closestPoint.x
        let dy = point.y - closestPoint.y
        return sqrt(dx * dx + dy * dy)
    }
}

// MARK: - 分段操作扩展
extension StrokeSegment {
    /// 分割分段
    /// - Parameter splitIndex: 分割点索引 (相对于父线条)
    /// - Returns: 分割后的两个分段，如果无法分割则返回nil
    func split(at splitIndex: Int) -> (StrokeSegment, StrokeSegment)? {
        guard splitIndex > startIndex && splitIndex < endIndex else {
            return nil
        }
        
        let firstSegment = StrokeSegment(
            startIndex: startIndex,
            endIndex: splitIndex,
            isErased: isErased
        )
        
        let secondSegment = StrokeSegment(
            startIndex: splitIndex + 1,
            endIndex: endIndex,
            isErased: isErased
        )
        
        return (firstSegment, secondSegment)
    }
    
    /// 合并两个相邻的分段
    /// - Parameter other: 要合并的分段
    /// - Returns: 合并后的分段，如果无法合并则返回nil
    func merge(with other: StrokeSegment) -> StrokeSegment? {
        // 检查是否可以合并
        guard !isErased && !other.isErased else { return nil }
        guard endIndex + 1 == other.startIndex || other.endIndex + 1 == startIndex else { return nil }
        
        let newStartIndex = min(startIndex, other.startIndex)
        let newEndIndex = max(endIndex, other.endIndex)
        
        return StrokeSegment(
            startIndex: newStartIndex,
            endIndex: newEndIndex,
            isErased: false
        )
    }
    
    /// 标记分段为已擦除
    mutating func markAsErased() {
        isErased = true
        invalidateBoundingBox()
    }
    
    /// 恢复已擦除的分段
    mutating func restore() {
        isErased = false
        invalidateBoundingBox()
    }
}

// MARK: - 调试和描述
extension StrokeSegment: CustomStringConvertible {
    var description: String {
        let status = isErased ? "擦除" : "正常"
        return "StrokeSegment([\(startIndex)-\(endIndex)], \(pointCount)点, \(status))"
    }
}

// MARK: - 分段管理器
/// 管理线条分段的工具类
struct StrokeSegmentManager {
    /// 默认分段大小
    static let defaultSegmentSize = 10
    
    /// 从点数组创建分段
    /// - Parameters:
    ///   - pointCount: 点的总数
    ///   - segmentSize: 每个分段的大小
    /// - Returns: 分段数组
    static func createSegments(pointCount: Int, segmentSize: Int = defaultSegmentSize) -> [StrokeSegment] {
        guard pointCount > 0 else { return [] }
        
        var segments: [StrokeSegment] = []
        var currentIndex = 0
        
        while currentIndex < pointCount {
            let endIndex = min(currentIndex + segmentSize - 1, pointCount - 1)
            let segment = StrokeSegment(startIndex: currentIndex, endIndex: endIndex)
            segments.append(segment)
            currentIndex = endIndex + 1
        }
        
        return segments
    }
    
    /// 优化分段列表 (合并相邻的未擦除分段)
    /// - Parameter segments: 原始分段列表
    /// - Returns: 优化后的分段列表
    static func optimizeSegments(_ segments: [StrokeSegment]) -> [StrokeSegment] {
        guard segments.count > 1 else { return segments }
        
        var optimized: [StrokeSegment] = []
        var current = segments[0]
        
        for i in 1..<segments.count {
            let next = segments[i]
            
            if let merged = current.merge(with: next) {
                current = merged
            } else {
                optimized.append(current)
                current = next
            }
        }
        
        optimized.append(current)
        return optimized
    }
}
