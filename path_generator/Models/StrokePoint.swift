//
//  StrokePoint.swift
//  path_generator
//
//  Created by AI Assistant on 2025/5/26.
//

import Foundation
import simd

/// 高度优化的点数据结构
/// 使用紧凑的内存布局，每个点仅占用7字节
struct StrokePoint: Codable, Equatable {
    // MARK: - Private Storage (7 bytes total)
    private let _x: UInt16          // 2字节 - 存储Float16位模式
    private let _y: UInt16          // 2字节 - 存储Float16位模式
    private let _pressure: UInt8    // 1字节 - 压力值0-255
    private let _timestamp: UInt16  // 2字节 - 相对时间戳(毫秒)

    // MARK: - Public Interface
    /// X坐标 (Float精度)
    var x: Float {
        Float16(bitPattern: _x).floatValue
    }

    /// Y坐标 (Float精度)
    var y: Float {
        Float16(bitPattern: _y).floatValue
    }

    /// 压力值 (0.0 - 1.0)
    var pressure: Float {
        Float(_pressure) / 255.0
    }

    /// 时间戳 (相对于线条开始时间，秒)
    var timestamp: TimeInterval {
        TimeInterval(_timestamp) / 1000.0
    }

    // MARK: - Initializers
    /// 创建新的笔画点
    /// - Parameters:
    ///   - x: X坐标
    ///   - y: Y坐标
    ///   - pressure: 压力值 (0.0-1.0)
    ///   - timestamp: 时间戳 (秒)
    init(x: Float, y: Float, pressure: Float, timestamp: TimeInterval) {
        self._x = Float16(x).bitPattern
        self._y = Float16(y).bitPattern
        self._pressure = UInt8(max(0, min(255, pressure * 255)))
        self._timestamp = UInt16(max(0, min(65535, timestamp * 1000)))
    }

    /// 便捷初始化器 - 使用CGPoint
    init(point: CGPoint, pressure: Float, timestamp: TimeInterval) {
        self.init(
            x: Float(point.x),
            y: Float(point.y),
            pressure: pressure,
            timestamp: timestamp
        )
    }
}

// MARK: - SIMD优化的向量运算
extension StrokePoint {
    /// 获取位置向量 (SIMD优化)
    var position: simd_float2 {
        simd_float2(x, y)
    }

    /// 获取CGPoint表示
    var cgPoint: CGPoint {
        CGPoint(x: CGFloat(x), y: CGFloat(y))
    }

    /// 计算到另一个点的距离 (SIMD优化)
    /// - Parameter other: 目标点
    /// - Returns: 欧几里得距离
    func distance(to other: StrokePoint) -> Float {
        return simd_distance(self.position, other.position)
    }

    /// 计算到另一个点的平方距离 (避免开方运算)
    /// - Parameter other: 目标点
    /// - Returns: 距离的平方
    func distanceSquared(to other: StrokePoint) -> Float {
        let delta = self.position - other.position
        return simd_dot(delta, delta)
    }

    /// 线性插值到另一个点
    /// - Parameters:
    ///   - other: 目标点
    ///   - factor: 插值因子 (0.0-1.0)
    /// - Returns: 插值后的点
    func interpolate(to other: StrokePoint, factor: Float) -> StrokePoint {
        let pos = simd_mix(self.position, other.position, simd_float2(factor, factor))
        let pressure = self.pressure + (other.pressure - self.pressure) * factor
        let timestamp = self.timestamp + (other.timestamp - self.timestamp) * Double(factor)

        return StrokePoint(
            x: pos.x,
            y: pos.y,
            pressure: pressure,
            timestamp: timestamp
        )
    }

    /// 计算两点间的方向向量 (归一化)
    /// - Parameter other: 目标点
    /// - Returns: 归一化的方向向量
    func direction(to other: StrokePoint) -> simd_float2 {
        let delta = other.position - self.position
        let length = simd_length(delta)
        return length > 0 ? delta / length : simd_float2(0, 0)
    }

    /// 计算两点间的角度 (弧度)
    /// - Parameter other: 目标点
    /// - Returns: 角度值 (弧度)
    func angle(to other: StrokePoint) -> Float {
        let delta = other.position - self.position
        return atan2(delta.y, delta.x)
    }
}

// MARK: - 几何计算扩展
extension StrokePoint {
    /// 计算三点间的角度变化
    /// - Parameters:
    ///   - previous: 前一个点
    ///   - next: 后一个点
    /// - Returns: 角度变化 (弧度)
    func angleChange(previous: StrokePoint, next: StrokePoint) -> Float {
        let angle1 = previous.angle(to: self)
        let angle2 = self.angle(to: next)
        var delta = angle2 - angle1

        // 标准化角度到 [-π, π]
        while delta > Float.pi { delta -= 2 * Float.pi }
        while delta < -Float.pi { delta += 2 * Float.pi }

        return abs(delta)
    }

    /// 检查三点是否近似共线
    /// - Parameters:
    ///   - previous: 前一个点
    ///   - next: 后一个点
    ///   - tolerance: 角度容差 (弧度)
    /// - Returns: 是否共线
    func isCollinear(with previous: StrokePoint, and next: StrokePoint, tolerance: Float = 0.1) -> Bool {
        return angleChange(previous: previous, next: next) < tolerance
    }
}

// MARK: - 调试和描述
extension StrokePoint: CustomStringConvertible {
    var description: String {
        return "StrokePoint(x: \(x), y: \(y), pressure: \(pressure), timestamp: \(timestamp))"
    }
}

// MARK: - Float16 扩展 (用于内存优化)
extension Float16 {
    /// 获取Float值
    var floatValue: Float {
        return Float(self)
    }
}
