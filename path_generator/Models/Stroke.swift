//
//  Stroke.swift
//  path_generator
//
//  Created by AI Assistant on 2025/5/26.
//

import Foundation
import CoreGraphics
import Combine

/// 线条数据模型 - 表示一条完整的笔画
class Stroke: ObservableObject, Identifiable, Codable {
    // MARK: - 基础属性
    /// 唯一标识符
    let id = UUID()

    /// 线条包含的所有点
    @Published var points: [StrokePoint] = []

    /// 线条样式
    @Published var style: StrokeStyle

    /// 线条分段 (用于部分擦除)
    @Published var segments: [StrokeSegment] = []

    /// 线条创建时间
    let createdAt: Date

    /// 线条最后修改时间
    @Published var modifiedAt: Date

    // MARK: - 缓存属性
    private var _boundingBox: CGRect?
    private var _totalLength: Float?
    private var _isComplete: Bool = false

    // MARK: - 计算属性
    /// 线条边界框
    var boundingBox: CGRect {
        if _boundingBox == nil {
            _boundingBox = calculateBoundingBox()
        }
        return _boundingBox!
    }

    /// 线条总长度
    var totalLength: Float {
        if _totalLength == nil {
            _totalLength = calculateTotalLength()
        }
        return _totalLength!
    }

    /// 线条是否为空
    var isEmpty: Bool {
        return points.isEmpty
    }

    /// 线条点数量
    var pointCount: Int {
        return points.count
    }

    /// 线条是否已完成 (不再添加新点)
    var isComplete: Bool {
        return _isComplete
    }

    /// 线条的起始时间戳
    var startTimestamp: TimeInterval {
        return points.first?.timestamp ?? 0
    }

    /// 线条的结束时间戳
    var endTimestamp: TimeInterval {
        return points.last?.timestamp ?? 0
    }

    /// 线条持续时间
    var duration: TimeInterval {
        return endTimestamp - startTimestamp
    }

    // MARK: - 初始化器
    /// 创建新线条
    /// - Parameter style: 线条样式
    init(style: StrokeStyle) {
        self.style = style
        self.createdAt = Date()
        self.modifiedAt = Date()
    }

    /// 从已有数据创建线条 (用于反序列化)
    init(points: [StrokePoint], style: StrokeStyle, segments: [StrokeSegment] = []) {
        self.points = points
        self.style = style
        self.segments = segments
        self.createdAt = Date()
        self.modifiedAt = Date()
        self._isComplete = true

        // 如果没有提供分段，自动创建
        if segments.isEmpty && !points.isEmpty {
            self.segments = StrokeSegmentManager.createSegments(pointCount: points.count)
        }
    }

    // MARK: - Codable 支持
    enum CodingKeys: String, CodingKey {
        case id, points, style, segments, createdAt, modifiedAt
    }

    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        // 注意：id 在解码时会被重新生成，这是正常的
        self.points = try container.decode([StrokePoint].self, forKey: .points)
        self.style = try container.decode(StrokeStyle.self, forKey: .style)
        self.segments = try container.decode([StrokeSegment].self, forKey: .segments)
        self.createdAt = try container.decode(Date.self, forKey: .createdAt)
        self.modifiedAt = try container.decode(Date.self, forKey: .modifiedAt)
        self._isComplete = true
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)

        try container.encode(id, forKey: .id)
        try container.encode(points, forKey: .points)
        try container.encode(style, forKey: .style)
        try container.encode(segments, forKey: .segments)
        try container.encode(createdAt, forKey: .createdAt)
        try container.encode(modifiedAt, forKey: .modifiedAt)
    }
}

// MARK: - 点操作扩展
extension Stroke {
    /// 添加新点到线条
    /// - Parameter point: 要添加的点
    func addPoint(_ point: StrokePoint) {
        guard !_isComplete else { return }

        points.append(point)
        modifiedAt = Date()

        // 失效缓存
        invalidateCache()

        // 更新分段
        updateSegments()
    }

    /// 批量添加点
    /// - Parameter newPoints: 要添加的点数组
    func addPoints(_ newPoints: [StrokePoint]) {
        guard !_isComplete && !newPoints.isEmpty else { return }

        points.append(contentsOf: newPoints)
        modifiedAt = Date()

        // 失效缓存
        invalidateCache()

        // 更新分段
        updateSegments()
    }

    /// 完成线条 (不再添加新点)
    func complete() {
        _isComplete = true
        modifiedAt = Date()

        // 最终优化
        optimizePoints()
        finalizeSegments()
    }

    /// 清空线条
    func clear() {
        points.removeAll()
        segments.removeAll()
        _isComplete = false
        modifiedAt = Date()
        invalidateCache()
    }

    /// 移除指定范围的点
    /// - Parameter range: 要移除的点的索引范围
    func removePoints(in range: Range<Int>) {
        guard range.lowerBound >= 0 && range.upperBound <= points.count else { return }

        points.removeSubrange(range)
        modifiedAt = Date()
        invalidateCache()

        // 重新创建分段
        recreateSegments()
    }
}

// MARK: - 几何计算扩展
extension Stroke {
    /// 计算边界框
    private func calculateBoundingBox() -> CGRect {
        guard !points.isEmpty else { return .zero }

        let xs = points.map { CGFloat($0.x) }
        let ys = points.map { CGFloat($0.y) }

        let minX = xs.min()!
        let maxX = xs.max()!
        let minY = ys.min()!
        let maxY = ys.max()!

        // 考虑线条宽度
        let maxWidth = CGFloat(style.baseWidth * 2)
        return CGRect(
            x: minX - maxWidth,
            y: minY - maxWidth,
            width: maxX - minX + maxWidth * 2,
            height: maxY - minY + maxWidth * 2
        )
    }

    /// 计算总长度
    private func calculateTotalLength() -> Float {
        guard points.count >= 2 else { return 0 }

        var length: Float = 0
        for i in 1..<points.count {
            length += points[i-1].distance(to: points[i])
        }
        return length
    }

    /// 检查点是否在线条附近
    /// - Parameters:
    ///   - point: 目标点
    ///   - tolerance: 容差距离
    /// - Returns: 是否在附近
    func contains(point: CGPoint, tolerance: CGFloat = 5.0) -> Bool {
        // 首先检查边界框
        let expandedBounds = boundingBox.insetBy(dx: -tolerance, dy: -tolerance)
        guard expandedBounds.contains(point) else { return false }

        // 检查各个分段
        for segment in segments {
            if !segment.isErased && segment.contains(point: point, in: points, tolerance: tolerance) {
                return true
            }
        }

        return false
    }

    /// 获取指定位置的点 (使用插值)
    /// - Parameter t: 位置参数 (0.0 - 1.0)
    /// - Returns: 插值后的点
    func getPoint(at t: Float) -> StrokePoint? {
        guard !points.isEmpty else { return nil }
        guard points.count > 1 else { return points.first }

        let clampedT = max(0, min(1, t))
        let targetLength = totalLength * clampedT

        var currentLength: Float = 0
        for i in 1..<points.count {
            let segmentLength = points[i-1].distance(to: points[i])

            if currentLength + segmentLength >= targetLength {
                let segmentT = (targetLength - currentLength) / segmentLength
                return points[i-1].interpolate(to: points[i], factor: segmentT)
            }

            currentLength += segmentLength
        }

        return points.last
    }
}

// MARK: - 分段管理扩展
extension Stroke {
    /// 更新分段
    private func updateSegments() {
        let segmentSize = StrokeSegmentManager.defaultSegmentSize
        let expectedSegmentCount = (points.count + segmentSize - 1) / segmentSize

        // 如果需要新分段
        while segments.count < expectedSegmentCount {
            let startIndex = segments.count * segmentSize
            let endIndex = min(startIndex + segmentSize - 1, points.count - 1)

            if startIndex < points.count {
                let segment = StrokeSegment(startIndex: startIndex, endIndex: endIndex)
                segments.append(segment)
            }
        }

        // 更新最后一个分段的结束索引
        if !segments.isEmpty && !points.isEmpty {
            let lastSegmentIndex = segments.count - 1
            let expectedEndIndex = points.count - 1
            segments[lastSegmentIndex].endIndex = expectedEndIndex
            segments[lastSegmentIndex].invalidateBoundingBox()
        }
    }

    /// 重新创建所有分段
    private func recreateSegments() {
        segments = StrokeSegmentManager.createSegments(pointCount: points.count)
    }

    /// 完成分段创建
    private func finalizeSegments() {
        segments = StrokeSegmentManager.optimizeSegments(segments)
    }

    /// 获取未擦除的分段
    var activeSegments: [StrokeSegment] {
        return segments.filter { !$0.isErased }
    }

    /// 获取已擦除的分段
    var erasedSegments: [StrokeSegment] {
        return segments.filter { $0.isErased }
    }
}

// MARK: - 优化扩展
extension Stroke {
    /// 优化点数据 (移除冗余点)
    private func optimizePoints() {
        guard points.count > 3 else { return }

        let originalCount = points.count

        // 应用轻量级优化：移除距离过近的点
        var optimizedPoints: [StrokePoint] = []
        optimizedPoints.append(points[0]) // 保留第一个点

        let minDistance: Float = 0.5 // 最小距离阈值

        for i in 1..<(points.count - 1) {
            let lastAdded = optimizedPoints.last!
            let current = points[i]

            // 如果距离足够远，或者压力变化显著，则保留点
            let distance = current.distance(to: lastAdded)
            let pressureDiff = abs(current.pressure - lastAdded.pressure)

            if distance >= minDistance || pressureDiff > 0.1 {
                optimizedPoints.append(current)
            }
        }

        optimizedPoints.append(points.last!) // 保留最后一个点

        // 只有在显著减少点数时才更新
        if optimizedPoints.count < Int(Double(originalCount) * 0.9) {
            points = optimizedPoints
            invalidateCache()
            print("🔧 Optimized stroke: \(originalCount) → \(points.count) points")
        }
    }

    /// 失效所有缓存
    private func invalidateCache() {
        _boundingBox = nil
        _totalLength = nil
    }
}

// MARK: - 调试和描述
extension Stroke: CustomStringConvertible {
    var description: String {
        let status = isComplete ? "已完成" : "进行中"
        return "Stroke(\(pointCount)点, \(segments.count)段, \(status))"
    }
}
