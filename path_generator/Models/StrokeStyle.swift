//
//  StrokeStyle.swift
//  path_generator
//
//  Created by AI Assistant on 2025/5/26.
//

import Foundation
import SwiftUI

/// 笔刷类型枚举
enum BrushType: UInt8, CaseIterable, Codable {
    case pen = 0        // 钢笔
    case pencil = 1     // 铅笔
    case marker = 2     // 马克笔
    case brush = 3      // 毛笔
    case highlighter = 4 // 荧光笔
    
    /// 笔刷显示名称
    var displayName: String {
        switch self {
        case .pen: return "钢笔"
        case .pencil: return "铅笔"
        case .marker: return "马克笔"
        case .brush: return "毛笔"
        case .highlighter: return "荧光笔"
        }
    }
    
    /// 默认压感敏感度
    var defaultPressureSensitivity: Float {
        switch self {
        case .pen: return 0.8
        case .pencil: return 1.2
        case .marker: return 0.5
        case .brush: return 1.5
        case .highlighter: return 0.3
        }
    }
    
    /// 默认不透明度
    var defaultOpacity: Float {
        switch self {
        case .pen: return 1.0
        case .pencil: return 0.8
        case .marker: return 0.9
        case .brush: return 0.7
        case .highlighter: return 0.4
        }
    }
}

/// 线条样式配置
struct StrokeStyle: Codable, Equatable {
    // MARK: - 基础属性
    /// 基础线条宽度 (点)
    var baseWidth: Float
    
    /// 压感敏感度 (0.0 - 2.0)
    var pressureSensitivity: Float
    
    /// 颜色 (ARGB格式，压缩存储)
    private var _color: UInt32
    
    /// 笔刷类型
    var brushType: BrushType
    
    /// 不透明度 (0-255)
    private var _opacity: UInt8
    
    // MARK: - 高级属性
    /// 是否启用拖尾效果
    var enableTail: Bool
    
    /// 拖尾长度 (0.0 - 1.0)
    var tailLength: Float
    
    /// 是否启用纹理
    var enableTexture: Bool
    
    /// 纹理强度 (0.0 - 1.0)
    var textureIntensity: Float
    
    // MARK: - 计算属性
    /// 颜色 (SwiftUI Color)
    var color: Color {
        get {
            let a = Float((_color >> 24) & 0xFF) / 255.0
            let r = Float((_color >> 16) & 0xFF) / 255.0
            let g = Float((_color >> 8) & 0xFF) / 255.0
            let b = Float(_color & 0xFF) / 255.0
            return Color(.sRGB, red: Double(r), green: Double(g), blue: Double(b), opacity: Double(a))
        }
        set {
            let components = newValue.cgColor?.components ?? [0, 0, 0, 1]
            let r = UInt32(min(255, max(0, components[0] * 255)))
            let g = UInt32(min(255, max(0, components[1] * 255)))
            let b = UInt32(min(255, max(0, components[2] * 255)))
            let a = UInt32(min(255, max(0, (components.count > 3 ? components[3] : 1.0) * 255)))
            _color = (a << 24) | (r << 16) | (g << 8) | b
        }
    }
    
    /// 不透明度 (0.0 - 1.0)
    var opacity: Float {
        get { Float(_opacity) / 255.0 }
        set { _opacity = UInt8(max(0, min(255, newValue * 255))) }
    }
    
    /// 获取RGBA颜色分量
    var colorComponents: (r: Float, g: Float, b: Float, a: Float) {
        let a = Float((_color >> 24) & 0xFF) / 255.0
        let r = Float((_color >> 16) & 0xFF) / 255.0
        let g = Float((_color >> 8) & 0xFF) / 255.0
        let b = Float(_color & 0xFF) / 255.0
        return (r, g, b, a)
    }
    
    // MARK: - 初始化器
    /// 创建新的线条样式
    /// - Parameters:
    ///   - baseWidth: 基础宽度
    ///   - color: 颜色
    ///   - brushType: 笔刷类型
    ///   - pressureSensitivity: 压感敏感度
    ///   - opacity: 不透明度
    init(
        baseWidth: Float = 2.0,
        color: Color = .black,
        brushType: BrushType = .pen,
        pressureSensitivity: Float? = nil,
        opacity: Float? = nil
    ) {
        self.baseWidth = baseWidth
        self.brushType = brushType
        self.pressureSensitivity = pressureSensitivity ?? brushType.defaultPressureSensitivity
        self.enableTail = false
        self.tailLength = 0.3
        self.enableTexture = false
        self.textureIntensity = 0.5
        self._color = 0
        self._opacity = 255
        
        // 初始化颜色
        self.color = color
        
        // 初始化不透明度
        self.opacity = opacity ?? brushType.defaultOpacity
    }
    
    /// 便捷初始化器 - 使用预设样式
    /// - Parameter brushType: 笔刷类型
    init(brushType: BrushType) {
        self.init(
            baseWidth: brushType == .highlighter ? 8.0 : 2.0,
            color: .black,
            brushType: brushType,
            pressureSensitivity: brushType.defaultPressureSensitivity,
            opacity: brushType.defaultOpacity
        )
    }
}

// MARK: - 样式计算扩展
extension StrokeStyle {
    /// 根据压力计算实际线条宽度
    /// - Parameter pressure: 压力值 (0.0 - 1.0)
    /// - Returns: 实际宽度
    func calculateWidth(for pressure: Float) -> Float {
        let normalizedPressure = max(0.0, min(1.0, pressure))
        let pressureEffect = pow(normalizedPressure, 1.0 / pressureSensitivity)
        
        // 最小宽度为基础宽度的30%
        let minWidthRatio: Float = 0.3
        let widthRatio = minWidthRatio + (1.0 - minWidthRatio) * pressureEffect
        
        return baseWidth * widthRatio
    }
    
    /// 计算拖尾透明度
    /// - Parameter distance: 距离笔尖的距离 (0.0 - 1.0)
    /// - Returns: 透明度倍数
    func calculateTailOpacity(at distance: Float) -> Float {
        guard enableTail && distance > 0 else { return 1.0 }
        
        let normalizedDistance = min(1.0, distance / tailLength)
        return 1.0 - normalizedDistance * 0.7 // 最多减少70%透明度
    }
    
    /// 获取纹理强度
    /// - Returns: 纹理强度值
    func getTextureIntensity() -> Float {
        return enableTexture ? textureIntensity : 0.0
    }
}

// MARK: - 预设样式
extension StrokeStyle {
    /// 默认钢笔样式
    static let defaultPen = StrokeStyle(brushType: .pen)
    
    /// 默认铅笔样式
    static let defaultPencil = StrokeStyle(brushType: .pencil)
    
    /// 默认马克笔样式
    static let defaultMarker = StrokeStyle(brushType: .marker)
    
    /// 默认毛笔样式
    static let defaultBrush = StrokeStyle(brushType: .brush)
    
    /// 默认荧光笔样式
    static let defaultHighlighter = StrokeStyle(brushType: .highlighter)
    
    /// 获取所有预设样式
    static var allPresets: [StrokeStyle] {
        return [defaultPen, defaultPencil, defaultMarker, defaultBrush, defaultHighlighter]
    }
}

// MARK: - 调试和描述
extension StrokeStyle: CustomStringConvertible {
    var description: String {
        return "StrokeStyle(width: \(baseWidth), type: \(brushType.displayName), pressure: \(pressureSensitivity), opacity: \(opacity))"
    }
}

// MARK: - 颜色扩展
extension UInt32 {
    /// 转换为Float4格式 (用于Metal渲染)
    func toFloat4() -> (Float, Float, Float, Float) {
        let a = Float((self >> 24) & 0xFF) / 255.0
        let r = Float((self >> 16) & 0xFF) / 255.0
        let g = Float((self >> 8) & 0xFF) / 255.0
        let b = Float(self & 0xFF) / 255.0
        return (r, g, b, a)
    }
}
