//
//  EnhancedStrokePoint.swift
//  path_generator
//
//  Created by AI Assistant on 2025/5/27.
//

import Foundation
import UIKit
import simd

/// 增强的笔画点数据结构
/// 支持Apple Pencil的高级特性：倾斜角度、方位角、压感预测等
struct EnhancedStrokePoint: Codable, Equatable {

    // MARK: - 基础数据 (继承自StrokePoint)
    /// X坐标
    let x: Float
    /// Y坐标
    let y: Float
    /// 压力值 (0.0 - 1.0)
    let pressure: Float
    /// 时间戳 (相对于线条开始时间，秒)
    let timestamp: TimeInterval

    // MARK: - Apple Pencil高级特性
    /// 倾斜角度 (0 - π/2, 0表示垂直，π/2表示平躺)
    let altitude: Float
    /// 方位角 (0 - 2π, 相对于设备坐标系)
    let azimuth: Float
    /// 输入类型
    let inputType: InputType

    // MARK: - 计算属性
    /// 笔尖椭圆的长轴半径 (基于倾斜角度)
    var tipMajorRadius: Float {
        return 1.0 / sin(altitude + 0.1) // 避免除零
    }

    /// 笔尖椭圆的短轴半径
    var tipMinorRadius: Float {
        return 1.0
    }

    /// 笔尖方向向量 (基于方位角)
    var tipDirection: simd_float2 {
        return simd_float2(cos(azimuth), sin(azimuth))
    }

    /// 获取位置向量 (SIMD优化)
    var position: simd_float2 {
        return simd_float2(x, y)
    }

    /// 获取CGPoint表示
    var cgPoint: CGPoint {
        return CGPoint(x: CGFloat(x), y: CGFloat(y))
    }

    // MARK: - 输入类型枚举
    enum InputType: UInt8, Codable, CaseIterable {
        case finger = 0     // 手指触摸
        case pencil = 1     // Apple Pencil
        case stylus = 2     // 第三方触控笔
        case mouse = 3      // 鼠标 (iPadOS 13.4+)

        var supportsPressure: Bool {
            switch self {
            case .finger, .mouse: return false
            case .pencil, .stylus: return true
            }
        }

        var supportsOrientation: Bool {
            return self == .pencil
        }
    }

    // MARK: - 初始化器

    /// 完整初始化器
    init(x: Float, y: Float,
         pressure: Float,
         timestamp: TimeInterval,
         altitude: Float = Float.pi / 2,
         azimuth: Float = 0,
         inputType: InputType = .finger) {
        self.x = x
        self.y = y
        self.pressure = max(0.0, min(1.0, pressure))
        self.timestamp = timestamp
        self.altitude = max(0.1, min(Float.pi / 2, altitude)) // 限制范围，避免极值
        self.azimuth = azimuth.truncatingRemainder(dividingBy: 2 * Float.pi) // 标准化到0-2π
        self.inputType = inputType
    }

    /// 从CGPoint创建
    init(point: CGPoint,
         pressure: Float,
         timestamp: TimeInterval,
         altitude: Float = Float.pi / 2,
         azimuth: Float = 0,
         inputType: InputType = .finger) {
        self.init(x: Float(point.x), y: Float(point.y),
                 pressure: pressure, timestamp: timestamp,
                 altitude: altitude, azimuth: azimuth, inputType: inputType)
    }

    /// 从UITouch创建 (自动检测Apple Pencil特性)
    static func from(_ touch: UITouch, in view: UIView,
                    relativeTimestamp: TimeInterval) -> EnhancedStrokePoint {
        let location = touch.location(in: view)
        let inputType: InputType = touch.type == .pencil ? .pencil : .finger

        var pressure: Float = 0.5
        var altitude: Float = Float.pi / 2
        var azimuth: Float = 0

        if inputType == .pencil {
            // Apple Pencil特性检测
            pressure = Float(touch.force / touch.maximumPossibleForce)
            altitude = Float(touch.altitudeAngle)
            azimuth = Float(touch.azimuthAngle(in: view))
        }

        return EnhancedStrokePoint(
            point: location,
            pressure: pressure,
            timestamp: relativeTimestamp,
            altitude: altitude,
            azimuth: azimuth,
            inputType: inputType
        )
    }

    /// 从基础StrokePoint转换
    static func from(_ strokePoint: StrokePoint,
                    inputType: InputType = .finger) -> EnhancedStrokePoint {
        return EnhancedStrokePoint(
            x: strokePoint.x,
            y: strokePoint.y,
            pressure: strokePoint.pressure,
            timestamp: strokePoint.timestamp,
            altitude: Float.pi / 2,
            azimuth: 0,
            inputType: inputType
        )
    }

    /// 转换为基础StrokePoint
    func toStrokePoint() -> StrokePoint {
        return StrokePoint(x: x, y: y, pressure: pressure, timestamp: timestamp)
    }
}

// MARK: - 几何计算扩展
extension EnhancedStrokePoint {

    /// 计算到另一个点的距离 (SIMD优化)
    func distance(to other: EnhancedStrokePoint) -> Float {
        return simd_distance(self.position, other.position)
    }

    /// 计算到另一个点的平方距离 (避免开方运算)
    func distanceSquared(to other: EnhancedStrokePoint) -> Float {
        let delta = self.position - other.position
        return simd_dot(delta, delta)
    }

    /// 线性插值到另一个点
    func interpolate(to other: EnhancedStrokePoint, factor: Float) -> EnhancedStrokePoint {
        let clampedFactor = max(0.0, min(1.0, factor))

        let pos = simd_mix(self.position, other.position, simd_float2(clampedFactor, clampedFactor))
        let pressure = self.pressure + (other.pressure - self.pressure) * clampedFactor
        let timestamp = self.timestamp + (other.timestamp - self.timestamp) * Double(clampedFactor)

        // 角度插值需要特殊处理
        let altitude = self.altitude + (other.altitude - self.altitude) * clampedFactor
        let azimuth = interpolateAngle(from: self.azimuth, to: other.azimuth, factor: clampedFactor)

        return EnhancedStrokePoint(
            x: pos.x, y: pos.y,
            pressure: pressure, timestamp: timestamp,
            altitude: altitude, azimuth: azimuth,
            inputType: self.inputType
        )
    }

    /// 角度插值 (处理2π边界)
    private func interpolateAngle(from start: Float, to end: Float, factor: Float) -> Float {
        let diff = end - start
        let adjustedDiff: Float

        if diff > Float.pi {
            adjustedDiff = diff - 2 * Float.pi
        } else if diff < -Float.pi {
            adjustedDiff = diff + 2 * Float.pi
        } else {
            adjustedDiff = diff
        }

        let result = start + adjustedDiff * factor
        return result.truncatingRemainder(dividingBy: 2 * Float.pi)
    }

    /// 计算两点间的方向向量 (归一化)
    func direction(to other: EnhancedStrokePoint) -> simd_float2 {
        let delta = other.position - self.position
        let length = simd_length(delta)
        return length > 0 ? delta / length : simd_float2(0, 0)
    }

    /// 计算笔尖椭圆在指定方向的半径
    func tipRadius(in direction: simd_float2) -> Float {
        // 将方向向量转换到笔尖坐标系
        let tipDir = self.tipDirection
        let cosTheta = simd_dot(direction, tipDir)
        let sinTheta = sqrt(1 - cosTheta * cosTheta)

        // 椭圆方程计算半径
        let a = tipMajorRadius
        let b = tipMinorRadius

        return (a * b) / sqrt(a * a * sinTheta * sinTheta + b * b * cosTheta * cosTheta)
    }
}

// MARK: - 压感分析扩展
extension EnhancedStrokePoint {

    /// 压感变化率 (相对于前一个点)
    func pressureChangeRate(from previous: EnhancedStrokePoint) -> Float {
        let timeDelta = Float(self.timestamp - previous.timestamp)
        guard timeDelta > 0 else { return 0 }

        let pressureDelta = self.pressure - previous.pressure
        return pressureDelta / timeDelta
    }

    /// 倾斜角度变化率
    func altitudeChangeRate(from previous: EnhancedStrokePoint) -> Float {
        let timeDelta = Float(self.timestamp - previous.timestamp)
        guard timeDelta > 0 else { return 0 }

        let altitudeDelta = self.altitude - previous.altitude
        return altitudeDelta / timeDelta
    }

    /// 计算绘制速度 (像素/秒)
    func velocity(from previous: EnhancedStrokePoint) -> Float {
        let timeDelta = Float(self.timestamp - previous.timestamp)
        guard timeDelta > 0 else { return 0 }

        let distance = self.distance(to: previous)
        return distance / timeDelta
    }

    /// 判断是否为有效的绘制点
    func isValidDrawingPoint(relativeTo previous: EnhancedStrokePoint?,
                           minDistance: Float = 1.0,
                           maxTimeDelta: TimeInterval = 0.1) -> Bool {
        guard let prev = previous else { return true }

        // 距离检查
        let distance = self.distance(to: prev)
        if distance < minDistance { return false }

        // 时间检查
        let timeDelta = self.timestamp - prev.timestamp
        if timeDelta > maxTimeDelta { return false }

        // 压感检查 (避免无效的压感值)
        if self.pressure <= 0 && self.inputType.supportsPressure { return false }

        return true
    }
}

// MARK: - 调试和描述
extension EnhancedStrokePoint: CustomStringConvertible {
    var description: String {
        let typeStr = inputType == .pencil ? "Pencil" : "Touch"
        return "\(typeStr)(\(String(format: "%.1f", x)), \(String(format: "%.1f", y)), p:\(String(format: "%.2f", pressure)), a:\(String(format: "%.2f", altitude)))"
    }
}
