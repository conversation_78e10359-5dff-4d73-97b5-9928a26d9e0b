//
//  PressureProcessor.swift
//  path_generator
//
//  Created by AI Assistant on 2025/5/27.
//

import Foundation
import simd

/// 高级压感处理器
/// 负责压感数据的平滑、预测、校准和优化
class PressureProcessor {

    // MARK: - 配置参数
    struct ProcessorConfig {
        // 平滑参数
        var smoothingWindow: Int = 5
        var smoothingFactor: Float = 0.3

        // 预测参数
        var predictionWindow: Int = 3
        var predictionWeight: Float = 0.2

        // 校准参数
        var minPressure: Float = 0.05
        var maxPressure: Float = 0.95
        var pressureDeadZone: Float = 0.02

        // 自适应参数
        var adaptiveEnabled: Bool = true
        var adaptiveWindow: Int = 50
        var adaptiveThreshold: Float = 0.1
    }

    private let config: ProcessorConfig
    private var pressureHistory: [Float] = []
    private var timestampHistory: [TimeInterval] = []
    private var calibrationData: CalibrationData

    // MARK: - 校准数据
    private struct CalibrationData {
        var userMinPressure: Float = 0.0
        var userMaxPressure: Float = 1.0
        var pressureBias: Float = 0.0
        var pressureScale: Float = 1.0
        var sampleCount: Int = 0

        mutating func updateCalibration(pressure: Float) {
            if sampleCount == 0 {
                userMinPressure = pressure
                userMaxPressure = pressure
            } else {
                userMinPressure = min(userMinPressure, pressure)
                userMaxPressure = max(userMaxPressure, pressure)
            }

            sampleCount += 1

            // 更新校准参数
            let range = userMaxPressure - userMinPressure
            if range > 0.1 { // 有效范围
                pressureBias = -userMinPressure / range
                pressureScale = 1.0 / range
            }
        }

        func calibrate(_ pressure: Float) -> Float {
            let calibrated = (pressure + pressureBias) * pressureScale
            return max(0.0, min(1.0, calibrated))
        }
    }

    init(config: ProcessorConfig = ProcessorConfig()) {
        self.config = config
        self.calibrationData = CalibrationData()
    }

    // MARK: - 主要处理方法

    /// 处理原始压感数据
    /// - Parameters:
    ///   - rawPressure: 原始压感值
    ///   - timestamp: 时间戳
    ///   - inputType: 输入类型
    /// - Returns: 处理后的压感值
    func processRawPressure(_ rawPressure: Float,
                           timestamp: TimeInterval,
                           inputType: EnhancedStrokePoint.InputType) -> Float {

        // 1. 输入类型处理
        var pressure = handleInputType(rawPressure, inputType: inputType)

        // 2. 死区处理
        pressure = applyDeadZone(pressure)

        // 3. 校准
        if config.adaptiveEnabled {
            calibrationData.updateCalibration(pressure: pressure)
            pressure = calibrationData.calibrate(pressure)
        }

        // 4. 更新历史
        updateHistory(pressure: pressure, timestamp: timestamp)

        // 5. 平滑处理
        pressure = applySmoothingFilter(pressure)

        // 6. 预测增强
        pressure = applyPredictiveEnhancement(pressure, timestamp: timestamp)

        // 7. 最终范围限制
        return max(config.minPressure, min(config.maxPressure, pressure))
    }

    /// 预测下一个压感值
    /// - Parameter currentTimestamp: 当前时间戳
    /// - Returns: 预测的压感值
    func predictNextPressure(at currentTimestamp: TimeInterval) -> Float? {
        guard pressureHistory.count >= config.predictionWindow else { return nil }

        // 使用线性回归预测
        let recentPressures = Array(pressureHistory.suffix(config.predictionWindow))
        let recentTimestamps = Array(timestampHistory.suffix(config.predictionWindow))

        return linearRegression(pressures: recentPressures,
                              timestamps: recentTimestamps,
                              targetTimestamp: currentTimestamp)
    }

    /// 获取压感变化趋势
    /// - Returns: 压感变化率 (正值表示增加，负值表示减少)
    func getPressureTrend() -> Float {
        guard pressureHistory.count >= 2 else { return 0 }

        let recent = pressureHistory.suffix(min(5, pressureHistory.count))
        let recentArray = Array(recent)

        var totalChange: Float = 0
        for i in 1..<recentArray.count {
            totalChange += recentArray[i] - recentArray[i-1]
        }

        return totalChange / Float(recentArray.count - 1)
    }

    /// 重置处理器状态
    func reset() {
        pressureHistory.removeAll()
        timestampHistory.removeAll()
        calibrationData = CalibrationData()
    }

    // MARK: - 私有处理方法

    /// 处理不同输入类型
    private func handleInputType(_ pressure: Float,
                                inputType: EnhancedStrokePoint.InputType) -> Float {
        switch inputType {
        case .finger:
            // 手指触摸：模拟压感
            return 0.5 + sin(Float(Date().timeIntervalSince1970) * 10) * 0.1

        case .pencil:
            // Apple Pencil：直接使用压感
            return pressure

        case .stylus:
            // 第三方触控笔：可能需要校准
            return pressure * 0.8 + 0.1

        case .mouse:
            // 鼠标：固定压感
            return 0.5
        }
    }

    /// 应用死区处理
    private func applyDeadZone(_ pressure: Float) -> Float {
        if pressure < config.pressureDeadZone {
            return 0.0
        } else if pressure > 1.0 - config.pressureDeadZone {
            return 1.0
        } else {
            // 重新映射到0-1范围
            let adjustedRange = 1.0 - 2 * config.pressureDeadZone
            let adjustedPressure = (pressure - config.pressureDeadZone) / adjustedRange
            return max(0.0, min(1.0, adjustedPressure))
        }
    }

    /// 更新历史数据
    private func updateHistory(pressure: Float, timestamp: TimeInterval) {
        pressureHistory.append(pressure)
        timestampHistory.append(timestamp)

        // 保持历史长度
        let maxHistory = max(config.smoothingWindow, config.adaptiveWindow)
        if pressureHistory.count > maxHistory {
            pressureHistory.removeFirst()
            timestampHistory.removeFirst()
        }
    }

    /// 应用平滑滤波器
    private func applySmoothingFilter(_ currentPressure: Float) -> Float {
        guard pressureHistory.count > 1 else { return currentPressure }

        let windowSize = min(config.smoothingWindow, pressureHistory.count - 1)
        let recentPressures = Array(pressureHistory.suffix(windowSize))

        // 加权移动平均
        var weightedSum: Float = 0
        var totalWeight: Float = 0

        for (index, pressure) in recentPressures.enumerated() {
            let weight = Float(index + 1) // 越新的权重越大
            weightedSum += pressure * weight
            totalWeight += weight
        }

        let smoothedPressure = weightedSum / totalWeight

        // 与当前值混合
        return currentPressure * (1 - config.smoothingFactor) +
               smoothedPressure * config.smoothingFactor
    }

    /// 应用预测增强
    private func applyPredictiveEnhancement(_ pressure: Float,
                                          timestamp: TimeInterval) -> Float {
        guard let predictedPressure = predictNextPressure(at: timestamp + 0.016) else {
            return pressure
        }

        // 将预测值与当前值混合
        return pressure * (1 - config.predictionWeight) +
               predictedPressure * config.predictionWeight
    }

    /// 线性回归预测
    private func linearRegression(pressures: [Float],
                                 timestamps: [TimeInterval],
                                 targetTimestamp: TimeInterval) -> Float? {
        guard pressures.count == timestamps.count && pressures.count >= 2 else {
            return nil
        }

        let n = Float(pressures.count)
        let sumX = timestamps.reduce(0, +)
        let sumY = pressures.reduce(0, +)
        let sumXY = zip(timestamps, pressures).reduce(0) { $0 + Double($1.0) * Double($1.1) }
        let sumXX = timestamps.reduce(0) { $0 + $1 * $1 }

        let denominator = n * Float(sumXX) - Float(sumX * sumX)
        guard abs(denominator) > 1e-6 else { return nil }

        let slope = (n * Float(sumXY) - Float(sumX) * sumY) / denominator
        let intercept = (sumY - slope * Float(sumX)) / n

        let prediction = slope * Float(targetTimestamp) + intercept
        return max(0.0, min(1.0, prediction))
    }
}

// MARK: - 压感分析工具
extension PressureProcessor {

    /// 压感统计信息
    struct PressureStatistics {
        let average: Float
        let minimum: Float
        let maximum: Float
        let variance: Float
        let trend: Float
        let stability: Float // 0-1，1表示非常稳定
    }

    /// 获取压感统计信息
    func getPressureStatistics() -> PressureStatistics? {
        guard pressureHistory.count >= 3 else { return nil }

        let pressures = pressureHistory
        let average = pressures.reduce(0, +) / Float(pressures.count)
        let minimum = pressures.min() ?? 0
        let maximum = pressures.max() ?? 1

        // 计算方差
        let variance = pressures.reduce(0) { $0 + pow($1 - average, 2) } / Float(pressures.count)

        // 计算趋势
        let trend = getPressureTrend()

        // 计算稳定性 (基于方差)
        let stability = max(0, 1 - variance * 4) // 方差越小越稳定

        return PressureStatistics(
            average: average,
            minimum: minimum,
            maximum: maximum,
            variance: variance,
            trend: trend,
            stability: stability
        )
    }

    /// 检测压感异常
    func detectPressureAnomalies() -> [PressureAnomaly] {
        guard pressureHistory.count >= config.smoothingWindow else { return [] }

        var anomalies: [PressureAnomaly] = []
        let recentPressures = Array(pressureHistory.suffix(config.smoothingWindow))

        // 检测突变
        for i in 1..<recentPressures.count {
            let change = abs(recentPressures[i] - recentPressures[i-1])
            if change > 0.3 { // 30%以上的突变
                anomalies.append(.suddenChange(index: i, magnitude: change))
            }
        }

        // 检测卡死
        let uniqueValues = Set(recentPressures.map { Int($0 * 100) })
        if uniqueValues.count <= 2 && recentPressures.count > 3 {
            anomalies.append(.stuck(value: recentPressures.last ?? 0))
        }

        return anomalies
    }

    enum PressureAnomaly {
        case suddenChange(index: Int, magnitude: Float)
        case stuck(value: Float)
        case outOfRange(value: Float)
    }
}
