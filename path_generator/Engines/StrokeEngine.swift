//
//  StrokeEngine.swift
//  path_generator
//
//  Created by AI Assistant on 2025/5/27.
//

import Foundation
import Combine
import UIKit

/// 线条处理引擎 - 负责线条的创建、处理和优化
class StrokeEngine: ObservableObject {
    // MARK: - Published Properties
    @Published var currentStroke: Stroke?
    @Published var completedStrokes: [Stroke] = []
    @Published var isProcessing: Bool = false

    // MARK: - Private Properties
    private var lastPoint: StrokePoint?
    private var pointBuffer: [StrokePoint] = []
    private var strokeStartTime: TimeInterval = 0

    // 性能监控
    private var performanceMetrics: PerformanceMetrics = PerformanceMetrics()

    // 配置参数
    private let minPointDistance: Float = 1.0  // 合理的最小点间距离
    private let maxPointsPerSecond: Int = 180   // 平衡的采样率
    private let bufferSize: Int = 5             // 恢复批量处理的缓冲区大小

    // MARK: - Public Methods

    /// 开始新的线条绘制
    /// - Parameters:
    ///   - point: 起始点位置
    ///   - pressure: 压力值
    ///   - style: 线条样式
    func startStroke(at point: CGPoint, pressure: Float, style: StrokeStyle) {
        let startTime = CACurrentMediaTime()
        performanceMetrics.startMeasuring("stroke_creation")

        // 创建新线条
        currentStroke = Stroke(style: style)
        strokeStartTime = startTime

        // 创建起始点
        let strokePoint = StrokePoint(
            point: point,
            pressure: pressure,
            timestamp: 0 // 相对时间戳从0开始
        )

        currentStroke?.addPoint(strokePoint)
        lastPoint = strokePoint
        pointBuffer.removeAll(keepingCapacity: true)

        isProcessing = true
        performanceMetrics.endMeasuring("stroke_creation")

        print("✏️ Started stroke at \(point) with pressure \(pressure)")
    }

    /// 添加新点到当前线条
    /// - Parameters:
    ///   - point: 点位置
    ///   - pressure: 压力值
    func addPoint(at point: CGPoint, pressure: Float) {
        guard let stroke = currentStroke else { return }

        let currentTime = CACurrentMediaTime()
        performanceMetrics.startMeasuring("point_processing")

        let relativeTimestamp = currentTime - strokeStartTime
        let strokePoint = StrokePoint(
            point: point,
            pressure: pressure,
            timestamp: relativeTimestamp
        )

        // 实时过滤
        if shouldAddPoint(strokePoint, currentTime: currentTime) {
            // 临时屏蔽批量处理：直接添加点，不使用缓冲区
            stroke.addPoint(strokePoint)

            // 屏蔽批量处理机制
            // pointBuffer.append(strokePoint)
            // if pointBuffer.count >= bufferSize {
            //     processBufferedPointsWithReplacement(for: stroke)
            // }
        }

        lastPoint = strokePoint
        performanceMetrics.endMeasuring("point_processing")
    }

    /// 结束当前线条绘制
    func endStroke() {
        guard let stroke = currentStroke else { return }

        performanceMetrics.startMeasuring("stroke_completion")

        // 屏蔽批量处理：不处理缓冲点
        // if !pointBuffer.isEmpty {
        //     processBufferedPointsWithReplacement(for: stroke)
        // }

        // 完成线条并进行优化
        stroke.complete()
        // 注意：优化现在由Stroke内部的optimizePoints()方法处理

        // 添加到完成列表
        completedStrokes.append(stroke)

        // 清理状态
        currentStroke = nil
        lastPoint = nil
        pointBuffer.removeAll()
        isProcessing = false

        performanceMetrics.endMeasuring("stroke_completion")
        performanceMetrics.logPerformance()

        print("✅ Completed stroke with \(stroke.pointCount) points")
    }

    /// 取消当前线条绘制
    func cancelStroke() {
        currentStroke = nil
        lastPoint = nil
        pointBuffer.removeAll()
        isProcessing = false

        print("❌ Cancelled current stroke")
    }

    /// 清空所有线条
    func clearAllStrokes() {
        completedStrokes.removeAll()
        cancelStroke()

        print("🗑️ Cleared all strokes")
    }

    /// 撤销最后一条线条
    func undoLastStroke() {
        if !completedStrokes.isEmpty {
            let removedStroke = completedStrokes.removeLast()
            print("↩️ Undid stroke with \(removedStroke.pointCount) points")
        }
    }

    // MARK: - Private Methods

    /// 判断是否应该添加点
    private func shouldAddPoint(_ point: StrokePoint, currentTime: TimeInterval) -> Bool {
        guard let last = lastPoint else { return true }

        // 距离过滤
        let distance = point.distance(to: last)
        if distance < minPointDistance {
            return false
        }

        // 时间过滤 - 限制最大采样率
        let timeDelta = point.timestamp - last.timestamp
        let minInterval = 1.0 / Double(maxPointsPerSecond)
        if timeDelta < minInterval {
            return false
        }

        return true
    }

    /// 处理缓冲区中的点（原方法，保留备用）
    private func processBufferedPoints(for stroke: Stroke) {
        guard !pointBuffer.isEmpty else { return }

        // 如果只有少量点，直接添加
        if pointBuffer.count <= 2 {
            for point in pointBuffer {
                stroke.addPoint(point)
            }
        } else {
            // 应用轻量级平滑算法
            let smoothedPoints = applyLightweightSmoothing(pointBuffer)
            for point in smoothedPoints {
                stroke.addPoint(point)
            }
        }

        pointBuffer.removeAll(keepingCapacity: true)
    }

    /// 处理缓冲区中的点并替换已添加的原始点
    private func processBufferedPointsWithReplacement(for stroke: Stroke) {
        guard !pointBuffer.isEmpty else { return }

        // 记录当前线条的点数，用于替换
        let originalPointCount = stroke.pointCount
        let bufferStartIndex = originalPointCount - pointBuffer.count

        // 如果只有少量点，不需要平滑处理
        if pointBuffer.count <= 2 {
            pointBuffer.removeAll(keepingCapacity: true)
            return
        }

        // 应用轻量级平滑算法
        let smoothedPoints = applyLightweightSmoothing(pointBuffer)

        // 替换线条中对应的点
        if bufferStartIndex >= 0 && bufferStartIndex < stroke.pointCount {
            stroke.replacePoints(from: bufferStartIndex, with: smoothedPoints)
        }

        pointBuffer.removeAll(keepingCapacity: true)
    }

    /// 应用轻量级平滑算法
    private func applyLightweightSmoothing(_ points: [StrokePoint]) -> [StrokePoint] {
        guard points.count >= 3 else { return points }

        var smoothedPoints: [StrokePoint] = []
        smoothedPoints.append(points[0]) // 保留第一个点

        // 简单的移动平均，只对中间点进行轻微平滑
        for i in 1..<(points.count - 1) {
            let prev = points[i - 1]
            let current = points[i]
            let next = points[i + 1]

            // 轻量级平滑：70%当前点 + 15%前一点 + 15%后一点
            let smoothX = current.x * 0.7 + prev.x * 0.15 + next.x * 0.15
            let smoothY = current.y * 0.7 + prev.y * 0.15 + next.y * 0.15
            let smoothPressure = current.pressure * 0.7 + prev.pressure * 0.15 + next.pressure * 0.15

            let smoothedPoint = StrokePoint(
                x: smoothX,
                y: smoothY,
                pressure: smoothPressure,
                timestamp: current.timestamp
            )

            smoothedPoints.append(smoothedPoint)
        }

        smoothedPoints.append(points.last!) // 保留最后一个点
        return smoothedPoints
    }

    /// 应用平滑滤波器（原有的重量级算法，保留备用）
    private func applySmoothingFilter(_ points: [StrokePoint]) -> [StrokePoint] {
        guard points.count >= 3 else { return points }

        var smoothedPoints: [StrokePoint] = []
        smoothedPoints.append(points[0]) // 保留第一个点

        // 简单的移动平均平滑
        for i in 1..<(points.count - 1) {
            let prev = points[i - 1]
            let current = points[i]
            let next = points[i + 1]

            // 位置平滑
            let smoothX = (prev.x + current.x + next.x) / 3.0
            let smoothY = (prev.y + current.y + next.y) / 3.0

            // 压力平滑
            let smoothPressure = (prev.pressure + current.pressure + next.pressure) / 3.0

            let smoothedPoint = StrokePoint(
                x: smoothX,
                y: smoothY,
                pressure: smoothPressure,
                timestamp: current.timestamp
            )

            smoothedPoints.append(smoothedPoint)
        }

        smoothedPoints.append(points.last!) // 保留最后一个点
        return smoothedPoints
    }


}

// MARK: - Performance Metrics
private struct PerformanceMetrics {
    private var measurements: [String: [TimeInterval]] = [:]
    private var startTimes: [String: TimeInterval] = [:]

    mutating func startMeasuring(_ operation: String) {
        startTimes[operation] = CACurrentMediaTime()
    }

    mutating func endMeasuring(_ operation: String) {
        guard let startTime = startTimes[operation] else { return }
        let duration = CACurrentMediaTime() - startTime

        if measurements[operation] == nil {
            measurements[operation] = []
        }
        measurements[operation]?.append(duration)

        startTimes.removeValue(forKey: operation)
    }

    func logPerformance() {
        for (operation, times) in measurements {
            let avgTime = times.reduce(0, +) / Double(times.count)
            let maxTime = times.max() ?? 0
            print("📊 \(operation): avg=\(String(format: "%.2f", avgTime * 1000))ms, max=\(String(format: "%.2f", maxTime * 1000))ms")
        }
    }
}

// MARK: - StrokeEngine Statistics
extension StrokeEngine {
    /// 获取统计信息
    var statistics: StrokeStatistics {
        let totalPoints = completedStrokes.reduce(0) { $0 + $1.pointCount }
        let currentPoints = currentStroke?.pointCount ?? 0

        return StrokeStatistics(
            totalStrokes: completedStrokes.count,
            totalPoints: totalPoints + currentPoints,
            currentStrokePoints: currentPoints,
            isDrawing: isProcessing
        )
    }
}

/// 线条统计信息
struct StrokeStatistics {
    let totalStrokes: Int
    let totalPoints: Int
    let currentStrokePoints: Int
    let isDrawing: Bool
}
