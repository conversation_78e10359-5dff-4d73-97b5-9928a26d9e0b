//
//  StrokeEngine.swift
//  path_generator
//
//  Created by AI Assistant on 2025/5/27.
//

import Foundation
import Combine
import UIKit

/// 线条处理引擎 - 负责线条的创建、处理和优化
class StrokeEngine: ObservableObject {
    // MARK: - Published Properties
    @Published var currentStroke: Stroke?
    @Published var isProcessing: Bool = false

    // MARK: - Dependencies
    var dataManager: DataManager?

    /// 已完成的线条数组 - 通过DataManager获取
    var completedStrokes: [Stroke] {
        return dataManager?.strokes ?? []
    }

    // MARK: - Private Properties
    private var lastPoint: StrokePoint?
    private var lastEnhancedPoint: EnhancedStrokePoint?
    private var pointBuffer: [StrokePoint] = []
    private var enhancedPointBuffer: [EnhancedStrokePoint] = []
    private var strokeStartTime: TimeInterval = 0

    // 性能监控
    private var performanceMetrics: PerformanceMetrics = PerformanceMetrics()

    // 增强压感处理器
    private var pressureProcessor: PressureProcessor = PressureProcessor()

    // 动态笔锋引擎
    private var brushTipEngine: BrushTipEngine = BrushTipEngine()

    // 配置参数 - 修复虚线问题
    private let minPointDistance: Float = 0.5  // 减小最小点间距离，避免虚线效果
    private let maxPointsPerSecond: Int = 300   // 提高采样率，确保连续性
    private let bufferSize: Int = 5             // 恢复批量处理的缓冲区大小

    // MARK: - Public Methods

    /// 开始新的线条绘制 (增强版本，支持增强样式和UITouch)
    /// - Parameters:
    ///   - touch: UITouch对象
    ///   - view: 视图对象
    ///   - enhancedStyle: 增强线条样式
    func startStroke(with touch: UITouch, in view: UIView, enhancedStyle: EnhancedStrokeStyle) {
        let startTime = CACurrentMediaTime()
        performanceMetrics.startMeasuring("enhanced_stroke_creation")

        // 创建新线条
        let newStroke = Stroke(style: enhancedStyle.toStrokeStyle())
        strokeStartTime = startTime

        // 配置笔锋引擎
        brushTipEngine = BrushTipEngine(config: enhancedStyle.getBrushTipEngineConfig())

        // 创建增强起始点
        let enhancedPoint = EnhancedStrokePoint.from(touch, in: view, relativeTimestamp: 0)

        // 处理压感
        let processedPressure = pressureProcessor.processRawPressure(
            enhancedPoint.pressure,
            timestamp: enhancedPoint.timestamp,
            inputType: enhancedPoint.inputType
        )

        // 计算笔锋状态
        let brushTipState = brushTipEngine.calculateBrushTip(for: enhancedPoint)

        // 创建基础点用于存储
        let strokePoint = StrokePoint(
            point: enhancedPoint.cgPoint,
            pressure: processedPressure,
            timestamp: 0
        )

        newStroke.addPoint(strokePoint)
        lastPoint = strokePoint
        lastEnhancedPoint = enhancedPoint
        pointBuffer.removeAll(keepingCapacity: true)
        enhancedPointBuffer.removeAll(keepingCapacity: true)

        // 在主线程中设置 @Published 属性
        DispatchQueue.main.async {
            self.currentStroke = newStroke
            self.isProcessing = true
        }

        performanceMetrics.endMeasuring("enhanced_stroke_creation")

        print("✏️ Started enhanced \(enhancedPoint.inputType) stroke with dynamic brush tip")
        print("   Tip size: \(brushTipState.width)x\(brushTipState.height), rotation: \(brushTipState.rotation)")
    }

    /// 开始新的线条绘制 (增强版本，支持UITouch)
    /// - Parameters:
    ///   - touch: UITouch对象
    ///   - view: 视图对象
    ///   - style: 线条样式
    func startStroke(with touch: UITouch, in view: UIView, style: StrokeStyle) {
        let startTime = CACurrentMediaTime()
        performanceMetrics.startMeasuring("stroke_creation")

        // 创建新线条
        let newStroke = Stroke(style: style)
        strokeStartTime = startTime

        // 创建增强起始点
        let enhancedPoint = EnhancedStrokePoint.from(touch, in: view, relativeTimestamp: 0)

        // 处理压感
        let processedPressure = pressureProcessor.processRawPressure(
            enhancedPoint.pressure,
            timestamp: enhancedPoint.timestamp,
            inputType: enhancedPoint.inputType
        )

        // 创建基础点用于存储
        let strokePoint = StrokePoint(
            point: enhancedPoint.cgPoint,
            pressure: processedPressure,
            timestamp: 0
        )

        newStroke.addPoint(strokePoint)
        lastPoint = strokePoint
        lastEnhancedPoint = enhancedPoint
        pointBuffer.removeAll(keepingCapacity: true)
        enhancedPointBuffer.removeAll(keepingCapacity: true)

        // 在主线程中设置 @Published 属性
        DispatchQueue.main.async {
            self.currentStroke = newStroke
            self.isProcessing = true
        }

        performanceMetrics.endMeasuring("stroke_creation")

        print("✏️ Started \(enhancedPoint.inputType) stroke at \(enhancedPoint.cgPoint) with pressure \(processedPressure)")
    }

    /// 开始新的线条绘制 (兼容版本)
    /// - Parameters:
    ///   - point: 起始点位置
    ///   - pressure: 压力值
    ///   - style: 线条样式
    func startStroke(at point: CGPoint, pressure: Float, style: StrokeStyle) {
        let startTime = CACurrentMediaTime()
        performanceMetrics.startMeasuring("stroke_creation")

        // 创建新线条
        let newStroke = Stroke(style: style)
        strokeStartTime = startTime

        // 创建起始点
        let strokePoint = StrokePoint(
            point: point,
            pressure: pressure,
            timestamp: 0 // 相对时间戳从0开始
        )

        newStroke.addPoint(strokePoint)
        lastPoint = strokePoint
        pointBuffer.removeAll(keepingCapacity: true)

        // 在主线程中设置 @Published 属性
        DispatchQueue.main.async {
            self.currentStroke = newStroke
            self.isProcessing = true
        }

        performanceMetrics.endMeasuring("stroke_creation")

        print("✏️ Started stroke at \(point) with pressure \(pressure)")
    }

    /// 添加新点到当前线条 (增强版本，支持动态笔锋)
    /// - Parameters:
    ///   - touch: UITouch对象
    ///   - view: 视图对象
    ///   - enableBrushTip: 是否启用动态笔锋计算
    func addPoint(with touch: UITouch, in view: UIView, enableBrushTip: Bool = true) {
        guard let stroke = currentStroke else { return }

        let currentTime = CACurrentMediaTime()
        performanceMetrics.startMeasuring("enhanced_point_processing")

        let relativeTimestamp = currentTime - strokeStartTime

        // 创建增强点
        let enhancedPoint = EnhancedStrokePoint.from(touch, in: view, relativeTimestamp: relativeTimestamp)

        // 处理压感
        let processedPressure = pressureProcessor.processRawPressure(
            enhancedPoint.pressure,
            timestamp: enhancedPoint.timestamp,
            inputType: enhancedPoint.inputType
        )

        // 计算动态笔锋状态
        var brushTipState: BrushTipEngine.BrushTipState? = nil
        if enableBrushTip {
            brushTipState = brushTipEngine.calculateBrushTip(for: enhancedPoint)
        }

        // 创建基础点
        let strokePoint = StrokePoint(
            point: enhancedPoint.cgPoint,
            pressure: processedPressure,
            timestamp: relativeTimestamp
        )

        // 实时过滤
        if shouldAddEnhancedPoint(enhancedPoint, currentTime: currentTime) {
            stroke.addPoint(strokePoint)

            // 如果启用了动态笔锋，记录笔锋状态变化
            if let tipState = brushTipState {
                // 这里可以添加笔锋状态的记录逻辑
                // 例如：存储到stroke的metadata中
                recordBrushTipState(tipState, for: stroke)
            }
        }

        lastPoint = strokePoint
        lastEnhancedPoint = enhancedPoint
        performanceMetrics.endMeasuring("enhanced_point_processing")
    }

    /// 添加新点到当前线条 (增强版本，支持UITouch)
    /// - Parameters:
    ///   - touch: UITouch对象
    ///   - view: 视图对象
    func addPoint(with touch: UITouch, in view: UIView) {
        guard let stroke = currentStroke else { return }

        let currentTime = CACurrentMediaTime()
        performanceMetrics.startMeasuring("point_processing")

        let relativeTimestamp = currentTime - strokeStartTime

        // 创建增强点
        let enhancedPoint = EnhancedStrokePoint.from(touch, in: view, relativeTimestamp: relativeTimestamp)

        // 处理压感
        let processedPressure = pressureProcessor.processRawPressure(
            enhancedPoint.pressure,
            timestamp: enhancedPoint.timestamp,
            inputType: enhancedPoint.inputType
        )

        // 创建基础点
        let strokePoint = StrokePoint(
            point: enhancedPoint.cgPoint,
            pressure: processedPressure,
            timestamp: relativeTimestamp
        )

        // 实时过滤
        if shouldAddEnhancedPoint(enhancedPoint, currentTime: currentTime) {
            stroke.addPoint(strokePoint)
        }

        lastPoint = strokePoint
        lastEnhancedPoint = enhancedPoint
        performanceMetrics.endMeasuring("point_processing")
    }

    /// 添加新点到当前线条 (兼容版本)
    /// - Parameters:
    ///   - point: 点位置
    ///   - pressure: 压力值
    func addPoint(at point: CGPoint, pressure: Float) {
        guard let stroke = currentStroke else { return }

        let currentTime = CACurrentMediaTime()
        performanceMetrics.startMeasuring("point_processing")

        let relativeTimestamp = currentTime - strokeStartTime
        let strokePoint = StrokePoint(
            point: point,
            pressure: pressure,
            timestamp: relativeTimestamp
        )

        // 实时过滤
        if shouldAddPoint(strokePoint, currentTime: currentTime) {
            // 临时屏蔽批量处理：直接添加点，不使用缓冲区
            stroke.addPoint(strokePoint)

            // 屏蔽批量处理机制
            // pointBuffer.append(strokePoint)
            // if pointBuffer.count >= bufferSize {
            //     processBufferedPointsWithReplacement(for: stroke)
            // }
        }

        lastPoint = strokePoint
        performanceMetrics.endMeasuring("point_processing")
    }

    /// 结束当前线条绘制
    func endStroke() {
        guard let stroke = currentStroke else { return }

        performanceMetrics.startMeasuring("stroke_completion")

        // 屏蔽批量处理：不处理缓冲点
        // if !pointBuffer.isEmpty {
        //     processBufferedPointsWithReplacement(for: stroke)
        // }

        // 完成线条并进行优化
        stroke.complete()
        // 注意：优化现在由Stroke内部的optimizePoints()方法处理

        // 添加到DataManager
        dataManager?.addStroke(stroke)

        // 清理状态 - 在主线程中修改 @Published 属性
        lastPoint = nil
        pointBuffer.removeAll()

        DispatchQueue.main.async {
            self.currentStroke = nil
            self.isProcessing = false
        }

        performanceMetrics.endMeasuring("stroke_completion")
        performanceMetrics.logPerformance()

        print("✅ Completed stroke with \(stroke.pointCount) points")
    }

    /// 取消当前线条绘制
    func cancelStroke() {
        DispatchQueue.main.async {
            self.currentStroke = nil
            self.isProcessing = false
        }
        lastPoint = nil
        lastEnhancedPoint = nil
        pointBuffer.removeAll()
        enhancedPointBuffer.removeAll()
        pressureProcessor.reset()
        brushTipEngine.reset()

        print("❌ Cancelled current stroke")
    }

    // MARK: - 笔锋状态管理

    /// 记录笔锋状态到线条元数据
    /// - Parameters:
    ///   - tipState: 笔锋状态
    ///   - stroke: 目标线条
    private func recordBrushTipState(_ tipState: BrushTipEngine.BrushTipState, for stroke: Stroke) {
        // 这里可以将笔锋状态信息存储到stroke的metadata中
        // 为了保持与现有架构的兼容性，暂时只记录关键信息

        // 可以考虑在Stroke中添加brushTipStates数组来存储完整的笔锋状态历史
        // 或者在渲染时实时计算笔锋状态

        // 目前先记录到性能监控中 (简化版本)
        print("🖌️ Brush tip: \(tipState.width)x\(tipState.height), rotation: \(tipState.rotation)")
    }

    /// 获取当前笔锋状态
    /// - Returns: 当前的笔锋状态，如果没有则返回nil
    func getCurrentBrushTipState() -> BrushTipEngine.BrushTipState? {
        guard let lastPoint = lastEnhancedPoint else { return nil }
        return brushTipEngine.calculateBrushTip(for: lastPoint)
    }

    /// 预测下一个笔锋状态
    /// - Parameter futurePoint: 预测的未来点
    /// - Returns: 预测的笔锋状态
    func predictBrushTipState(for futurePoint: EnhancedStrokePoint) -> BrushTipEngine.BrushTipState {
        return brushTipEngine.calculateBrushTip(for: futurePoint)
    }

    /// 清空所有线条
    func clearAllStrokes() {
        dataManager?.clearAllStrokes()
        cancelStroke()

        print("🗑️ Cleared all strokes")
    }

    /// 撤销最后一条线条
    func undoLastStroke() {
        if let removedStroke = dataManager?.removeLastStroke() {
            print("↩️ Undid stroke with \(removedStroke.pointCount) points")
        }
    }

    // MARK: - Private Methods

    /// 判断是否应该添加增强点 - 支持Apple Pencil特性
    private func shouldAddEnhancedPoint(_ point: EnhancedStrokePoint, currentTime: TimeInterval) -> Bool {
        guard let last = lastEnhancedPoint else { return true }

        // 距离过滤 - 只在距离非常近时才过滤
        let distance = point.distance(to: last)
        if distance < minPointDistance {
            return false
        }

        // 时间过滤 - 根据输入类型调整
        let timeDelta = point.timestamp - last.timestamp
        let baseInterval = 1.0 / Double(maxPointsPerSecond)

        // Apple Pencil可以有更高的采样率
        let minInterval = point.inputType == .pencil ? baseInterval * 0.5 : baseInterval

        // 如果距离较大，即使时间间隔短也要添加点（避免虚线）
        if distance > minPointDistance * 2.0 {
            return true
        }

        // 压感变化显著时也要添加点
        let pressureChange = abs(point.pressure - last.pressure)
        if pressureChange > 0.1 {
            return true
        }

        // Apple Pencil倾斜角度变化显著时添加点
        if point.inputType == .pencil {
            let altitudeChange = abs(point.altitude - last.altitude)
            let azimuthChange = abs(point.azimuth - last.azimuth)

            if altitudeChange > 0.1 || azimuthChange > 0.2 {
                return true
            }
        }

        // 正常的时间过滤
        if timeDelta < minInterval {
            return false
        }

        return true
    }

    /// 判断是否应该添加点 - 修复虚线问题 (兼容版本)
    private func shouldAddPoint(_ point: StrokePoint, currentTime: TimeInterval) -> Bool {
        guard let last = lastPoint else { return true }

        // 距离过滤 - 只在距离非常近时才过滤
        let distance = point.distance(to: last)
        if distance < minPointDistance {
            return false
        }

        // 放宽时间过滤 - 确保连续性，特别是慢速绘制时
        let timeDelta = point.timestamp - last.timestamp
        let minInterval = 1.0 / Double(maxPointsPerSecond)

        // 如果距离较大，即使时间间隔短也要添加点（避免虚线）
        if distance > minPointDistance * 2.0 {
            return true
        }

        // 正常的时间过滤
        if timeDelta < minInterval {
            return false
        }

        return true
    }

    /// 处理缓冲区中的点（原方法，保留备用）
    private func processBufferedPoints(for stroke: Stroke) {
        guard !pointBuffer.isEmpty else { return }

        // 如果只有少量点，直接添加
        if pointBuffer.count <= 2 {
            for point in pointBuffer {
                stroke.addPoint(point)
            }
        } else {
            // 应用轻量级平滑算法
            let smoothedPoints = applyLightweightSmoothing(pointBuffer)
            for point in smoothedPoints {
                stroke.addPoint(point)
            }
        }

        pointBuffer.removeAll(keepingCapacity: true)
    }

    /// 处理缓冲区中的点并替换已添加的原始点
    private func processBufferedPointsWithReplacement(for stroke: Stroke) {
        guard !pointBuffer.isEmpty else { return }

        // 记录当前线条的点数，用于替换
        let originalPointCount = stroke.pointCount
        let bufferStartIndex = originalPointCount - pointBuffer.count

        // 如果只有少量点，不需要平滑处理
        if pointBuffer.count <= 2 {
            pointBuffer.removeAll(keepingCapacity: true)
            return
        }

        // 应用轻量级平滑算法
        let smoothedPoints = applyLightweightSmoothing(pointBuffer)

        // 替换线条中对应的点
        if bufferStartIndex >= 0 && bufferStartIndex < stroke.pointCount {
            stroke.replacePoints(from: bufferStartIndex, with: smoothedPoints)
        }

        pointBuffer.removeAll(keepingCapacity: true)
    }

    /// 应用轻量级平滑算法
    private func applyLightweightSmoothing(_ points: [StrokePoint]) -> [StrokePoint] {
        guard points.count >= 3 else { return points }

        var smoothedPoints: [StrokePoint] = []
        smoothedPoints.append(points[0]) // 保留第一个点

        // 简单的移动平均，只对中间点进行轻微平滑
        for i in 1..<(points.count - 1) {
            let prev = points[i - 1]
            let current = points[i]
            let next = points[i + 1]

            // 轻量级平滑：70%当前点 + 15%前一点 + 15%后一点
            let smoothX = current.x * 0.7 + prev.x * 0.15 + next.x * 0.15
            let smoothY = current.y * 0.7 + prev.y * 0.15 + next.y * 0.15
            let smoothPressure = current.pressure * 0.7 + prev.pressure * 0.15 + next.pressure * 0.15

            let smoothedPoint = StrokePoint(
                x: smoothX,
                y: smoothY,
                pressure: smoothPressure,
                timestamp: current.timestamp
            )

            smoothedPoints.append(smoothedPoint)
        }

        smoothedPoints.append(points.last!) // 保留最后一个点
        return smoothedPoints
    }

    /// 应用平滑滤波器（原有的重量级算法，保留备用）
    private func applySmoothingFilter(_ points: [StrokePoint]) -> [StrokePoint] {
        guard points.count >= 3 else { return points }

        var smoothedPoints: [StrokePoint] = []
        smoothedPoints.append(points[0]) // 保留第一个点

        // 简单的移动平均平滑
        for i in 1..<(points.count - 1) {
            let prev = points[i - 1]
            let current = points[i]
            let next = points[i + 1]

            // 位置平滑
            let smoothX = (prev.x + current.x + next.x) / 3.0
            let smoothY = (prev.y + current.y + next.y) / 3.0

            // 压力平滑
            let smoothPressure = (prev.pressure + current.pressure + next.pressure) / 3.0

            let smoothedPoint = StrokePoint(
                x: smoothX,
                y: smoothY,
                pressure: smoothPressure,
                timestamp: current.timestamp
            )

            smoothedPoints.append(smoothedPoint)
        }

        smoothedPoints.append(points.last!) // 保留最后一个点
        return smoothedPoints
    }


}

// MARK: - Performance Metrics
private struct PerformanceMetrics {
    private var measurements: [String: [TimeInterval]] = [:]
    private var startTimes: [String: TimeInterval] = [:]

    mutating func startMeasuring(_ operation: String) {
        startTimes[operation] = CACurrentMediaTime()
    }

    mutating func endMeasuring(_ operation: String) {
        guard let startTime = startTimes[operation] else { return }
        let duration = CACurrentMediaTime() - startTime

        if measurements[operation] == nil {
            measurements[operation] = []
        }
        measurements[operation]?.append(duration)

        startTimes.removeValue(forKey: operation)
    }

    func logPerformance() {
        for (operation, times) in measurements {
            let avgTime = times.reduce(0, +) / Double(times.count)
            let maxTime = times.max() ?? 0
            print("📊 \(operation): avg=\(String(format: "%.2f", avgTime * 1000))ms, max=\(String(format: "%.2f", maxTime * 1000))ms")
        }
    }
}

// MARK: - StrokeEngine Statistics
extension StrokeEngine {
    /// 获取统计信息
    var statistics: StrokeStatistics {
        let totalPoints = completedStrokes.reduce(0) { $0 + $1.pointCount }
        let currentPoints = currentStroke?.pointCount ?? 0

        return StrokeStatistics(
            totalStrokes: completedStrokes.count,
            totalPoints: totalPoints + currentPoints,
            currentStrokePoints: currentPoints,
            isDrawing: isProcessing
        )
    }
}

/// 线条统计信息
struct StrokeStatistics {
    let totalStrokes: Int
    let totalPoints: Int
    let currentStrokePoints: Int
    let isDrawing: Bool
}
