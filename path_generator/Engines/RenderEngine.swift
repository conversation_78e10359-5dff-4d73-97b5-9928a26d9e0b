//
//  RenderEngine.swift
//  path_generator
//
//  Created by AI Assistant on 2024/12/27.
//

import Foundation
import CoreGraphics
import UIKit
import Combine

/// 渲染引擎 - 负责高性能的线条渲染
class RenderEngine: ObservableObject {

    // MARK: - 依赖
    var dataManager: DataManager?

    // MARK: - 配置参数
    private let enableViewportCulling: Bool = true
    private let enableLOD: Bool = true
    private let batchRenderingThreshold: Int = 10

    // MARK: - 缓存
    private var pathCache: [UUID: CGPath] = [:]
    private var boundingBoxCache: [UUID: CGRect] = [:]

    // MARK: - 性能监控
    private var renderMetrics = RenderMetrics()

    // MARK: - 主要渲染方法

    /// 渲染多个线条（带优化）
    /// - Parameters:
    ///   - strokes: 要渲染的线条数组
    ///   - context: 图形上下文
    ///   - viewport: 视口区域
    ///   - scaleFactor: 缩放因子（保留参数兼容性，但不用于LOD）
    func renderStrokes(_ strokes: [Stroke],
                      in context: CGContext,
                      viewport: CGRect,
                      scaleFactor: CGFloat = 1.0) {
        renderMetrics.startFrame()

        // 视口裁剪：只渲染可见的线条
        let visibleStrokes = enableViewportCulling ?
            cullStrokesOutsideViewport(strokes, viewport: viewport) : strokes

        renderMetrics.recordVisibleStrokes(visibleStrokes.count, total: strokes.count)

        // 批量渲染相同样式的线条
        if visibleStrokes.count >= batchRenderingThreshold {
            renderStrokesBatched(visibleStrokes, in: context, scaleFactor: scaleFactor)
        } else {
            // 单独渲染每个线条
            for stroke in visibleStrokes {
                renderSingleStroke(stroke, in: context, scaleFactor: scaleFactor)
            }
        }

        renderMetrics.endFrame()
    }

    /// 渲染单个线条
    /// - Parameters:
    ///   - stroke: 要渲染的线条
    ///   - context: 图形上下文
    ///   - scaleFactor: 缩放因子
    func renderSingleStroke(_ stroke: Stroke,
                           in context: CGContext,
                           scaleFactor: CGFloat = 1.0) {
        guard !stroke.points.isEmpty else { return }

        // 保持完整矢量路径，不进行LOD优化
        let optimizedPoints = stroke.points

        // 创建临时线条用于渲染（保持与StrokeRenderer一致）
        let tempStroke = Stroke(style: stroke.style)
        for point in optimizedPoints {
            tempStroke.addPoint(point)
        }

        // 使用StrokeRenderer确保渲染一致性
        StrokeRenderer.renderStroke(tempStroke, in: context, viewBounds: CGRect.infinite)

        renderMetrics.recordStrokeRendered(pointCount: optimizedPoints.count)
    }

    // MARK: - 私有方法

    /// 视口裁剪：移除视口外的线条
    private func cullStrokesOutsideViewport(_ strokes: [Stroke], viewport: CGRect) -> [Stroke] {
        return strokes.filter { stroke in
            let boundingBox = getCachedBoundingBox(for: stroke)
            return viewport.intersects(boundingBox)
        }
    }

    /// 批量渲染相同样式的线条
    private func renderStrokesBatched(_ strokes: [Stroke],
                                     in context: CGContext,
                                     scaleFactor: CGFloat) {
        // 为了保持渲染一致性，即使是批量渲染也使用StrokeRenderer
        // 保持完整矢量路径，不进行LOD优化
        for stroke in strokes {
            // 创建临时线条用于渲染，保持所有原始点
            let tempStroke = Stroke(style: stroke.style)
            for point in stroke.points {
                tempStroke.addPoint(point)
            }

            // 使用StrokeRenderer确保渲染一致性
            StrokeRenderer.renderStroke(tempStroke, in: context, viewBounds: CGRect.infinite)
        }
    }

    /// 计算LOD级别
    private func calculateLODLevel(for scaleFactor: CGFloat) -> LODLevel {
        switch scaleFactor {
        case 0..<0.5:
            return .low
        case 0.5..<1.5:
            return .medium
        default:
            return .high
        }
    }

    /// 应用LOD优化
    private func applyLOD(to points: [StrokePoint], level: LODLevel) -> [StrokePoint] {
        switch level {
        case .low:
            // 只保留25%的点
            return stride(from: 0, to: points.count, by: 4).map { points[$0] }
        case .medium:
            // 保留50%的点
            return stride(from: 0, to: points.count, by: 2).map { points[$0] }
        case .high:
            // 保留所有点
            return points
        }
    }

    /// 获取缓存的路径 - 现在不再使用，因为统一使用StrokeRenderer
    private func getCachedPath(for stroke: Stroke, points: [StrokePoint]) -> CGPath {
        // 这个方法现在不再使用，因为我们统一使用StrokeRenderer
        // 保留是为了避免编译错误，但实际不会被调用
        return CGMutablePath()
    }

    /// 获取缓存的边界框
    private func getCachedBoundingBox(for stroke: Stroke) -> CGRect {
        if let cachedBox = boundingBoxCache[stroke.id] {
            return cachedBox
        }

        let boundingBox = stroke.boundingBox
        boundingBoxCache[stroke.id] = boundingBox
        return boundingBox
    }

    // 注意：createPath和setupRenderingState方法已移除
    // 现在统一使用StrokeRenderer来确保渲染一致性

    /// 清除缓存
    func clearCache() {
        pathCache.removeAll()
        boundingBoxCache.removeAll()
    }

    /// 获取渲染统计
    var statistics: RenderStatistics {
        return renderMetrics.getStatistics()
    }
}

// MARK: - 支持类型

/// LOD级别
enum LODLevel {
    case low, medium, high
}

/// 渲染统计
struct RenderStatistics {
    let averageFPS: Double
    let strokesRendered: Int
    let pointsRendered: Int
    let cacheHitRate: Double
}

/// 渲染性能监控
private class RenderMetrics {
    private var frameStartTime: TimeInterval = 0
    private var frameTimes: [TimeInterval] = []
    private var strokesRendered: Int = 0
    private var pointsRendered: Int = 0
    private var visibleStrokesCount: Int = 0
    private var totalStrokesCount: Int = 0

    func startFrame() {
        frameStartTime = CACurrentMediaTime()
    }

    func endFrame() {
        let frameTime = CACurrentMediaTime() - frameStartTime
        frameTimes.append(frameTime)

        // 保持最近100帧的数据
        if frameTimes.count > 100 {
            frameTimes.removeFirst()
        }
    }

    func recordStrokeRendered(pointCount: Int) {
        strokesRendered += 1
        pointsRendered += pointCount
    }

    func recordVisibleStrokes(_ visible: Int, total: Int) {
        visibleStrokesCount = visible
        totalStrokesCount = total
    }

    func getStatistics() -> RenderStatistics {
        let averageFrameTime = frameTimes.isEmpty ? 0 : frameTimes.reduce(0, +) / Double(frameTimes.count)
        let fps = averageFrameTime > 0 ? 1.0 / averageFrameTime : 0
        let cullRate = totalStrokesCount > 0 ? Double(visibleStrokesCount) / Double(totalStrokesCount) : 1.0

        return RenderStatistics(
            averageFPS: fps,
            strokesRendered: strokesRendered,
            pointsRendered: pointsRendered,
            cacheHitRate: cullRate
        )
    }
}
