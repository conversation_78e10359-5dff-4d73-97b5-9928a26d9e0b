//
//  RenderEngine.swift
//  path_generator
//
//  Created by AI Assistant on 2024/12/27.
//

import Foundation
import CoreGraphics
import UIKit

/// 渲染引擎 - 负责高性能的线条渲染
class RenderEngine {
    
    // MARK: - 配置参数
    private let enableViewportCulling: Bool = true
    private let enableLOD: Bool = true
    private let batchRenderingThreshold: Int = 10
    
    // MARK: - 缓存
    private var pathCache: [UUID: CGPath] = [:]
    private var boundingBoxCache: [UUID: CGRect] = [:]
    
    // MARK: - 性能监控
    private var renderMetrics = RenderMetrics()
    
    // MARK: - 主要渲染方法
    
    /// 渲染多个线条（带优化）
    /// - Parameters:
    ///   - strokes: 要渲染的线条数组
    ///   - context: 图形上下文
    ///   - viewport: 视口区域
    ///   - scaleFactor: 缩放因子（用于LOD）
    func renderStrokes(_ strokes: [Stroke], 
                      in context: CGContext, 
                      viewport: CGRect, 
                      scaleFactor: CGFloat = 1.0) {
        renderMetrics.startFrame()
        
        // 视口裁剪：只渲染可见的线条
        let visibleStrokes = enableViewportCulling ? 
            cullStrokesOutsideViewport(strokes, viewport: viewport) : strokes
        
        renderMetrics.recordVisibleStrokes(visibleStrokes.count, total: strokes.count)
        
        // 批量渲染相同样式的线条
        if visibleStrokes.count >= batchRenderingThreshold {
            renderStrokesBatched(visibleStrokes, in: context, scaleFactor: scaleFactor)
        } else {
            // 单独渲染每个线条
            for stroke in visibleStrokes {
                renderSingleStroke(stroke, in: context, scaleFactor: scaleFactor)
            }
        }
        
        renderMetrics.endFrame()
    }
    
    /// 渲染单个线条
    /// - Parameters:
    ///   - stroke: 要渲染的线条
    ///   - context: 图形上下文
    ///   - scaleFactor: 缩放因子
    func renderSingleStroke(_ stroke: Stroke, 
                           in context: CGContext, 
                           scaleFactor: CGFloat = 1.0) {
        guard !stroke.points.isEmpty else { return }
        
        // LOD优化：根据缩放级别调整渲染质量
        let lodLevel = calculateLODLevel(for: scaleFactor)
        let optimizedPoints = enableLOD ? 
            applyLOD(to: stroke.points, level: lodLevel) : stroke.points
        
        // 使用缓存的路径或创建新路径
        let path = getCachedPath(for: stroke, points: optimizedPoints)
        
        // 设置渲染状态
        setupRenderingState(for: stroke.style, in: context)
        
        // 渲染路径
        context.addPath(path)
        context.strokePath()
        
        renderMetrics.recordStrokeRendered(pointCount: optimizedPoints.count)
    }
    
    // MARK: - 私有方法
    
    /// 视口裁剪：移除视口外的线条
    private func cullStrokesOutsideViewport(_ strokes: [Stroke], viewport: CGRect) -> [Stroke] {
        return strokes.filter { stroke in
            let boundingBox = getCachedBoundingBox(for: stroke)
            return viewport.intersects(boundingBox)
        }
    }
    
    /// 批量渲染相同样式的线条
    private func renderStrokesBatched(_ strokes: [Stroke], 
                                     in context: CGContext, 
                                     scaleFactor: CGFloat) {
        // 按样式分组 - 使用简单的字符串标识
        let groupedStrokes = Dictionary(grouping: strokes) { stroke in
            "\(stroke.style.brushType.rawValue)_\(stroke.style.baseWidth)_\(stroke.style.opacity)"
        }
        
        // 批量渲染每组
        for (_, strokeGroup) in groupedStrokes {
            guard let firstStroke = strokeGroup.first else { continue }
            
            // 设置一次渲染状态
            setupRenderingState(for: firstStroke.style, in: context)
            
            // 渲染组内所有线条
            for stroke in strokeGroup {
                let lodLevel = calculateLODLevel(for: scaleFactor)
                let optimizedPoints = enableLOD ? 
                    applyLOD(to: stroke.points, level: lodLevel) : stroke.points
                
                let path = getCachedPath(for: stroke, points: optimizedPoints)
                context.addPath(path)
            }
            
            // 一次性绘制所有路径
            context.strokePath()
        }
    }
    
    /// 计算LOD级别
    private func calculateLODLevel(for scaleFactor: CGFloat) -> LODLevel {
        switch scaleFactor {
        case 0..<0.5:
            return .low
        case 0.5..<1.5:
            return .medium
        default:
            return .high
        }
    }
    
    /// 应用LOD优化
    private func applyLOD(to points: [StrokePoint], level: LODLevel) -> [StrokePoint] {
        switch level {
        case .low:
            // 只保留25%的点
            return stride(from: 0, to: points.count, by: 4).map { points[$0] }
        case .medium:
            // 保留50%的点
            return stride(from: 0, to: points.count, by: 2).map { points[$0] }
        case .high:
            // 保留所有点
            return points
        }
    }
    
    /// 获取缓存的路径
    private func getCachedPath(for stroke: Stroke, points: [StrokePoint]) -> CGPath {
        if let cachedPath = pathCache[stroke.id] {
            return cachedPath
        }
        
        let path = createPath(from: points)
        pathCache[stroke.id] = path
        return path
    }
    
    /// 获取缓存的边界框
    private func getCachedBoundingBox(for stroke: Stroke) -> CGRect {
        if let cachedBox = boundingBoxCache[stroke.id] {
            return cachedBox
        }
        
        let boundingBox = stroke.boundingBox
        boundingBoxCache[stroke.id] = boundingBox
        return boundingBox
    }
    
    /// 创建路径
    private func createPath(from points: [StrokePoint]) -> CGPath {
        let path = CGMutablePath()
        guard !points.isEmpty else { return path }
        
        let firstPoint = CGPoint(x: CGFloat(points[0].x), y: CGFloat(points[0].y))
        path.move(to: firstPoint)
        
        for i in 1..<points.count {
            let point = CGPoint(x: CGFloat(points[i].x), y: CGFloat(points[i].y))
            path.addLine(to: point)
        }
        
        return path
    }
    
    /// 设置渲染状态
    private func setupRenderingState(for style: StrokeStyle, in context: CGContext) {
        context.setLineWidth(CGFloat(style.baseWidth))
        
        // 设置颜色 - 使用colorComponents
        let color = CGColor(
            red: CGFloat(style.colorComponents.r),
            green: CGFloat(style.colorComponents.g),
            blue: CGFloat(style.colorComponents.b),
            alpha: CGFloat(style.colorComponents.a)
        )
        context.setStrokeColor(color)
        context.setAlpha(CGFloat(style.opacity))
        context.setLineCap(.round)
        context.setLineJoin(.round)
    }
    
    /// 清除缓存
    func clearCache() {
        pathCache.removeAll()
        boundingBoxCache.removeAll()
    }
    
    /// 获取渲染统计
    var statistics: RenderStatistics {
        return renderMetrics.getStatistics()
    }
}

// MARK: - 支持类型

/// LOD级别
enum LODLevel {
    case low, medium, high
}

/// 渲染统计
struct RenderStatistics {
    let averageFPS: Double
    let strokesRendered: Int
    let pointsRendered: Int
    let cacheHitRate: Double
}

/// 渲染性能监控
private class RenderMetrics {
    private var frameStartTime: TimeInterval = 0
    private var frameTimes: [TimeInterval] = []
    private var strokesRendered: Int = 0
    private var pointsRendered: Int = 0
    private var visibleStrokesCount: Int = 0
    private var totalStrokesCount: Int = 0
    
    func startFrame() {
        frameStartTime = CACurrentMediaTime()
    }
    
    func endFrame() {
        let frameTime = CACurrentMediaTime() - frameStartTime
        frameTimes.append(frameTime)
        
        // 保持最近100帧的数据
        if frameTimes.count > 100 {
            frameTimes.removeFirst()
        }
    }
    
    func recordStrokeRendered(pointCount: Int) {
        strokesRendered += 1
        pointsRendered += pointCount
    }
    
    func recordVisibleStrokes(_ visible: Int, total: Int) {
        visibleStrokesCount = visible
        totalStrokesCount = total
    }
    
    func getStatistics() -> RenderStatistics {
        let averageFrameTime = frameTimes.isEmpty ? 0 : frameTimes.reduce(0, +) / Double(frameTimes.count)
        let fps = averageFrameTime > 0 ? 1.0 / averageFrameTime : 0
        let cullRate = totalStrokesCount > 0 ? Double(visibleStrokesCount) / Double(totalStrokesCount) : 1.0
        
        return RenderStatistics(
            averageFPS: fps,
            strokesRendered: strokesRendered,
            pointsRendered: pointsRendered,
            cacheHitRate: cullRate
        )
    }
}
