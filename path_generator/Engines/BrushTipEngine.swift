//
//  BrushTipEngine.swift
//  path_generator
//
//  Created by AI Assistant on 2025/5/27.
//

import Foundation
import UIKit
import simd

/// 动态笔锋效果引擎
/// 负责根据Apple Pencil的倾斜角度、压力等参数计算笔锋形状和效果
class BrushTipEngine {

    // MARK: - 笔锋配置
    struct BrushTipConfig {
        // 基础参数
        var baseWidth: Float = 5.0
        var baseHeight: Float = 5.0

        // 倾斜敏感度
        var tiltSensitivity: Float = 1.0
        var maxTiltRatio: Float = 3.0  // 最大长宽比
        var minTiltRatio: Float = 0.3  // 最小长宽比

        // 压感敏感度
        var pressureSensitivity: Float = 0.8
        var minPressureScale: Float = 0.2
        var maxPressureScale: Float = 2.0

        // 方向敏感度
        var azimuthSensitivity: Float = 1.0
        var rotationSmoothing: Float = 0.3

        // 笔刷类型特定参数
        var brushType: BrushType = .pen
        var tipSharpness: Float = 0.8  // 笔尖锐度 0-1
        var edgeSoftness: Float = 0.2  // 边缘柔和度 0-1
    }

    private let config: BrushTipConfig
    private var lastTipState: BrushTipState?

    init(config: BrushTipConfig = BrushTipConfig()) {
        self.config = config
    }

    // MARK: - 笔锋状态
    struct BrushTipState {
        // 几何属性
        let width: Float          // 笔锋宽度
        let height: Float         // 笔锋高度
        let rotation: Float       // 旋转角度 (弧度)
        let aspectRatio: Float    // 长宽比

        // 形状属性
        let sharpness: Float      // 锐度
        let softness: Float       // 柔和度
        let opacity: Float        // 不透明度

        // 位置属性
        let position: simd_float2 // 笔锋中心位置
        let pressure: Float       // 当前压力

        // 计算属性
        var majorAxis: Float { max(width, height) }
        var minorAxis: Float { min(width, height) }
        var area: Float { width * height * Float.pi / 4 }

        /// 获取笔锋的边界矩形
        var boundingRect: CGRect {
            let halfWidth = CGFloat(width / 2)
            let halfHeight = CGFloat(height / 2)
            return CGRect(
                x: CGFloat(position.x) - halfWidth,
                y: CGFloat(position.y) - halfHeight,
                width: CGFloat(width),
                height: CGFloat(height)
            )
        }

        /// 获取旋转变换矩阵
        var rotationTransform: CGAffineTransform {
            return CGAffineTransform(rotationAngle: CGFloat(rotation))
        }
    }

    // MARK: - 主要计算方法

    /// 计算笔锋状态
    /// - Parameter point: 增强的笔画点
    /// - Returns: 计算得到的笔锋状态
    func calculateBrushTip(for point: EnhancedStrokePoint) -> BrushTipState {
        // 1. 基础尺寸计算
        let baseSize = calculateBaseSize(pressure: point.pressure)

        // 2. 倾斜效果计算
        let tiltEffect = calculateTiltEffect(altitude: point.altitude, azimuth: point.azimuth)

        // 3. 最终尺寸
        let finalWidth = baseSize.width * tiltEffect.widthScale
        let finalHeight = baseSize.height * tiltEffect.heightScale

        // 4. 旋转角度计算
        let rotation = calculateRotation(azimuth: point.azimuth, lastState: lastTipState)

        // 5. 形状属性计算
        let shapeProperties = calculateShapeProperties(point: point, tiltEffect: tiltEffect)

        let tipState = BrushTipState(
            width: finalWidth,
            height: finalHeight,
            rotation: rotation,
            aspectRatio: finalHeight / finalWidth,
            sharpness: shapeProperties.sharpness,
            softness: shapeProperties.softness,
            opacity: shapeProperties.opacity,
            position: point.position,
            pressure: point.pressure
        )

        lastTipState = tipState
        return tipState
    }

    /// 计算笔锋间的插值状态
    /// - Parameters:
    ///   - from: 起始笔锋状态
    ///   - to: 目标笔锋状态
    ///   - factor: 插值因子 (0-1)
    /// - Returns: 插值后的笔锋状态
    func interpolateBrushTip(from: BrushTipState, to: BrushTipState, factor: Float) -> BrushTipState {
        let clampedFactor = max(0.0, min(1.0, factor))

        return BrushTipState(
            width: lerp(from.width, to.width, clampedFactor),
            height: lerp(from.height, to.height, clampedFactor),
            rotation: lerpAngle(from.rotation, to.rotation, clampedFactor),
            aspectRatio: lerp(from.aspectRatio, to.aspectRatio, clampedFactor),
            sharpness: lerp(from.sharpness, to.sharpness, clampedFactor),
            softness: lerp(from.softness, to.softness, clampedFactor),
            opacity: lerp(from.opacity, to.opacity, clampedFactor),
            position: simd_mix(from.position, to.position, simd_float2(clampedFactor, clampedFactor)),
            pressure: lerp(from.pressure, to.pressure, clampedFactor)
        )
    }

    // MARK: - 私有计算方法

    /// 计算基础尺寸
    private func calculateBaseSize(pressure: Float) -> (width: Float, height: Float) {
        let pressureScale = config.minPressureScale +
                           (config.maxPressureScale - config.minPressureScale) *
                           pow(pressure, config.pressureSensitivity)

        let width = config.baseWidth * pressureScale
        let height = config.baseHeight * pressureScale

        return (width, height)
    }

    /// 计算倾斜效果
    private func calculateTiltEffect(altitude: Float, azimuth: Float) -> (widthScale: Float, heightScale: Float) {
        // 倾斜角度越小（越平），笔锋越扁
        let tiltFactor = sin(altitude) // 0 (平躺) 到 1 (垂直)
        let tiltRatio = config.minTiltRatio + (1.0 - config.minTiltRatio) * tiltFactor

        // 根据笔刷类型调整倾斜效果
        let adjustedTiltRatio = adjustTiltForBrushType(tiltRatio)

        // 计算长轴和短轴的缩放
        let aspectRatio = 1.0 + (config.maxTiltRatio - 1.0) * (1.0 - tiltRatio) * config.tiltSensitivity

        return (widthScale: 1.0, heightScale: aspectRatio)
    }

    /// 根据笔刷类型调整倾斜效果
    private func adjustTiltForBrushType(_ tiltRatio: Float) -> Float {
        switch config.brushType {
        case .pen:
            return tiltRatio * 0.8 + 0.2  // 钢笔倾斜效果较小
        case .pencil:
            return tiltRatio  // 铅笔正常倾斜效果
        case .marker:
            return tiltRatio * 1.5  // 马克笔倾斜效果较大
        case .brush:
            return tiltRatio * 2.0  // 毛笔倾斜效果最大
        case .highlighter:
            return 0.3  // 荧光笔基本不受倾斜影响
        }
    }

    /// 计算旋转角度
    private func calculateRotation(azimuth: Float, lastState: BrushTipState?) -> Float {
        var targetRotation = azimuth * config.azimuthSensitivity

        // 平滑旋转变化
        if let last = lastState {
            targetRotation = lerpAngle(last.rotation, targetRotation, config.rotationSmoothing)
        }

        return targetRotation
    }

    /// 计算形状属性
    private func calculateShapeProperties(point: EnhancedStrokePoint,
                                        tiltEffect: (widthScale: Float, heightScale: Float)) ->
                                        (sharpness: Float, softness: Float, opacity: Float) {

        // 锐度：压力越大越锐利，倾斜时变柔和
        let pressureSharpness = point.pressure * config.tipSharpness
        let tiltSoftening = (1.0 - sin(point.altitude)) * 0.3
        let sharpness = max(0.1, pressureSharpness - tiltSoftening)

        // 柔和度：与锐度相反
        let softness = config.edgeSoftness + (1.0 - sharpness) * 0.3

        // 不透明度：基于压力和笔刷类型
        let opacity = calculateOpacityForBrushType(pressure: point.pressure)

        return (sharpness: sharpness, softness: softness, opacity: opacity)
    }

    /// 根据笔刷类型计算不透明度
    private func calculateOpacityForBrushType(pressure: Float) -> Float {
        switch config.brushType {
        case .pen:
            return 1.0  // 钢笔完全不透明
        case .pencil:
            return 0.7 + pressure * 0.3  // 铅笔半透明
        case .marker:
            return 0.8 + pressure * 0.2  // 马克笔较不透明
        case .brush:
            return 0.5 + pressure * 0.5  // 毛笔透明度变化大
        case .highlighter:
            return 0.3  // 荧光笔半透明
        }
    }

    // MARK: - 工具方法

    /// 线性插值
    private func lerp(_ a: Float, _ b: Float, _ t: Float) -> Float {
        return a + (b - a) * t
    }

    /// 角度插值（处理2π边界）
    private func lerpAngle(_ a: Float, _ b: Float, _ t: Float) -> Float {
        let diff = b - a
        let adjustedDiff: Float

        if diff > Float.pi {
            adjustedDiff = diff - 2 * Float.pi
        } else if diff < -Float.pi {
            adjustedDiff = diff + 2 * Float.pi
        } else {
            adjustedDiff = diff
        }

        let result = a + adjustedDiff * t
        return result.truncatingRemainder(dividingBy: 2 * Float.pi)
    }

    /// 重置引擎状态
    func reset() {
        lastTipState = nil
    }

    /// 更新配置
    func updateConfig(_ newConfig: BrushTipConfig) {
        // 这里可以添加配置更新逻辑
        // 注意：实际实现中应该创建新的引擎实例或者安全地更新配置
    }
}

// MARK: - 笔锋渲染辅助
extension BrushTipEngine {

    /// 生成笔锋路径
    /// - Parameter tipState: 笔锋状态
    /// - Returns: 用于渲染的CGPath
    func generateTipPath(for tipState: BrushTipState) -> CGPath {
        let path = CGMutablePath()

        // 创建椭圆路径
        let rect = tipState.boundingRect
        path.addEllipse(in: rect, transform: tipState.rotationTransform)

        return path
    }

    /// 计算笔锋覆盖的像素区域
    /// - Parameter tipState: 笔锋状态
    /// - Returns: 影响的像素区域
    func calculateAffectedRegion(for tipState: BrushTipState) -> CGRect {
        let softnessPadding = CGFloat(tipState.softness * 10) // 柔和度影响范围
        return tipState.boundingRect.insetBy(dx: -softnessPadding, dy: -softnessPadding)
    }

    /// 计算两个笔锋状态之间的差异度
    /// - Parameters:
    ///   - state1: 第一个笔锋状态
    ///   - state2: 第二个笔锋状态
    /// - Returns: 差异度 (0-1，0表示完全相同)
    func calculateTipDifference(_ state1: BrushTipState, _ state2: BrushTipState) -> Float {
        let sizeDiff = abs(state1.width - state2.width) / max(state1.width, state2.width)
        let shapeDiff = abs(state1.aspectRatio - state2.aspectRatio)
        let rotationDiff = abs(state1.rotation - state2.rotation) / Float.pi
        let pressureDiff = abs(state1.pressure - state2.pressure)

        return (sizeDiff + shapeDiff + rotationDiff + pressureDiff) / 4.0
    }
}
