# 性能基准和测试计划

## 性能目标定义

### 1. 核心性能指标

#### 1.1 响应性能
- **输入延迟**: < 16ms (60 FPS)
- **渲染帧率**: 稳定60 FPS，峰值120 FPS (ProMotion设备)
- **触摸响应**: < 8ms (从触摸到像素显示)
- **压感响应**: < 12ms (压力变化到宽度变化)

#### 1.2 内存性能
- **基础内存占用**: < 50MB (空白画布)
- **每条线条内存**: < 1KB (100个点的线条)
- **内存增长率**: < 10MB/1000条线条
- **内存峰值**: < 200MB (复杂文档)

#### 1.3 存储性能
- **压缩比率**: > 80% (相比原始数据)
- **保存速度**: < 100ms (1000条线条)
- **加载速度**: < 200ms (1000条线条)
- **文件大小**: < 1MB (A4页面密集书写)

#### 1.4 电池性能
- **连续书写**: > 8小时 (iPad Pro)
- **待机功耗**: < 1%/小时
- **CPU使用率**: < 30% (活跃书写时)
- **GPU使用率**: < 40% (复杂渲染时)

### 2. 设备兼容性基准

#### 2.1 最低配置 (iPad 9th Gen)
- 输入延迟: < 20ms
- 渲染帧率: 稳定30 FPS
- 最大线条数: 5000条
- 内存限制: 150MB

#### 2.2 推荐配置 (iPad Air 5th Gen)
- 输入延迟: < 16ms
- 渲染帧率: 稳定60 FPS
- 最大线条数: 10000条
- 内存限制: 200MB

#### 2.3 最佳配置 (iPad Pro M2)
- 输入延迟: < 12ms
- 渲染帧率: 稳定120 FPS
- 最大线条数: 20000条
- 内存限制: 500MB

## 测试框架设计

### 1. 自动化测试框架

#### 1.1 性能测试基类
```swift
class PerformanceTestBase: XCTestCase {
    var strokeEngine: StrokeEngine!
    var renderEngine: RenderEngine!
    var performanceMonitor: PerformanceMonitor!
    
    override func setUp() {
        super.setUp()
        strokeEngine = StrokeEngine()
        renderEngine = RenderEngine()
        performanceMonitor = PerformanceMonitor()
    }
    
    func measureLatency<T>(operation: () -> T) -> (result: T, latency: TimeInterval) {
        let startTime = CACurrentMediaTime()
        let result = operation()
        let endTime = CACurrentMediaTime()
        return (result, endTime - startTime)
    }
    
    func measureMemory<T>(operation: () -> T) -> (result: T, memoryDelta: Int64) {
        let startMemory = getMemoryUsage()
        let result = operation()
        let endMemory = getMemoryUsage()
        return (result, endMemory - startMemory)
    }
}
```

#### 1.2 输入延迟测试
```swift
class InputLatencyTests: PerformanceTestBase {
    
    func testTouchToRenderLatency() {
        let expectation = XCTestExpectation(description: "Touch to render latency")
        var latencies: [TimeInterval] = []
        
        // 模拟100次触摸输入
        for _ in 0..<100 {
            let (_, latency) = measureLatency {
                let point = CGPoint(x: 100, y: 100)
                strokeEngine.startStroke(at: point, pressure: 0.5, style: defaultStyle)
                renderEngine.renderFrame()
            }
            latencies.append(latency)
        }
        
        let averageLatency = latencies.reduce(0, +) / Double(latencies.count)
        let maxLatency = latencies.max()!
        
        XCTAssertLessThan(averageLatency, 0.016, "Average latency should be < 16ms")
        XCTAssertLessThan(maxLatency, 0.025, "Max latency should be < 25ms")
        
        expectation.fulfill()
        wait(for: [expectation], timeout: 10.0)
    }
    
    func testPressureResponseLatency() {
        measure(metrics: [XCTClockMetric()]) {
            for pressure in stride(from: 0.0, through: 1.0, by: 0.1) {
                let point = CGPoint(x: 100, y: 100)
                strokeEngine.addPoint(at: point, pressure: Float(pressure))
            }
        }
    }
}
```

#### 1.3 内存性能测试
```swift
class MemoryPerformanceTests: PerformanceTestBase {
    
    func testMemoryUsagePerStroke() {
        let (_, memoryDelta) = measureMemory {
            createTestStroke(pointCount: 100)
        }
        
        // 每条线条应该占用少于1KB内存
        XCTAssertLessThan(memoryDelta, 1024, "Memory per stroke should be < 1KB")
    }
    
    func testMemoryLeaks() {
        let initialMemory = getMemoryUsage()
        
        // 创建和销毁1000条线条
        for _ in 0..<1000 {
            autoreleasepool {
                let stroke = createTestStroke(pointCount: 50)
                strokeEngine.addStroke(stroke)
            }
        }
        
        strokeEngine.clearAllStrokes()
        
        // 强制垃圾回收
        for _ in 0..<3 {
            autoreleasepool { }
        }
        
        let finalMemory = getMemoryUsage()
        let memoryLeak = finalMemory - initialMemory
        
        // 内存泄漏应该小于10MB
        XCTAssertLessThan(memoryLeak, 10 * 1024 * 1024, "Memory leak should be < 10MB")
    }
}
```

#### 1.4 渲染性能测试
```swift
class RenderingPerformanceTests: PerformanceTestBase {
    
    func testFrameRate() {
        let frameRateMonitor = FrameRateMonitor()
        frameRateMonitor.startMonitoring()
        
        // 模拟复杂场景渲染
        let strokes = createComplexScene(strokeCount: 1000)
        
        measure(metrics: [XCTClockMetric()]) {
            for _ in 0..<60 { // 渲染60帧
                renderEngine.render(strokes: strokes)
            }
        }
        
        frameRateMonitor.stopMonitoring()
        let averageFrameRate = frameRateMonitor.averageFrameRate
        
        XCTAssertGreaterThan(averageFrameRate, 55.0, "Frame rate should be > 55 FPS")
    }
    
    func testViewportCulling() {
        let allStrokes = createTestStrokes(count: 10000)
        let viewport = CGRect(x: 0, y: 0, width: 1024, height: 768)
        
        measure(metrics: [XCTClockMetric()]) {
            let visibleStrokes = renderEngine.cullStrokes(allStrokes, viewport: viewport)
            renderEngine.render(strokes: visibleStrokes)
        }
    }
}
```

### 2. 压力测试

#### 2.1 大数据量测试
```swift
class StressTests: PerformanceTestBase {
    
    func testLargeDocumentPerformance() {
        // 创建包含10000条线条的大文档
        let largeDocument = createLargeDocument(strokeCount: 10000)
        
        measure(metrics: [XCTMemoryMetric(), XCTClockMetric()]) {
            renderEngine.render(strokes: largeDocument.strokes)
        }
    }
    
    func testContinuousDrawing() {
        // 模拟连续绘制30分钟
        let testDuration: TimeInterval = 30 * 60 // 30分钟
        let startTime = CACurrentMediaTime()
        
        while CACurrentMediaTime() - startTime < testDuration {
            autoreleasepool {
                simulateDrawingSession(duration: 10) // 10秒绘制会话
                Thread.sleep(forTimeInterval: 1) // 1秒休息
            }
        }
        
        // 检查内存是否稳定
        let finalMemory = getMemoryUsage()
        XCTAssertLessThan(finalMemory, 300 * 1024 * 1024, "Memory should be < 300MB after stress test")
    }
}
```

#### 2.2 并发测试
```swift
class ConcurrencyTests: PerformanceTestBase {
    
    func testMultiThreadedAccess() {
        let expectation = XCTestExpectation(description: "Multi-threaded access")
        expectation.expectedFulfillmentCount = 10
        
        // 10个并发线程同时访问
        for i in 0..<10 {
            DispatchQueue.global(qos: .userInitiated).async {
                for j in 0..<100 {
                    let point = CGPoint(x: i * 10, y: j * 10)
                    self.strokeEngine.addPoint(at: point, pressure: 0.5)
                }
                expectation.fulfill()
            }
        }
        
        wait(for: [expectation], timeout: 30.0)
        
        // 验证数据完整性
        XCTAssertEqual(strokeEngine.totalPointCount, 1000)
    }
}
```

### 3. 存储性能测试

#### 3.1 压缩效率测试
```swift
class CompressionTests: PerformanceTestBase {
    
    func testCompressionRatio() {
        let testStroke = createTestStroke(pointCount: 1000)
        let originalSize = calculateUncompressedSize(testStroke)
        
        let compressor = StrokeCompressor()
        let compressedData = compressor.compress(testStroke)
        
        let compressionRatio = Double(compressedData.count) / Double(originalSize)
        
        XCTAssertLessThan(compressionRatio, 0.2, "Compression ratio should be < 20%")
    }
    
    func testCompressionSpeed() {
        let testStrokes = createTestStrokes(count: 1000)
        
        measure(metrics: [XCTClockMetric()]) {
            let compressor = StrokeCompressor()
            for stroke in testStrokes {
                _ = compressor.compress(stroke)
            }
        }
    }
    
    func testDecompressionAccuracy() {
        let originalStroke = createTestStroke(pointCount: 500)
        let compressor = StrokeCompressor()
        
        let compressedData = compressor.compress(originalStroke)
        let decompressedStroke = compressor.decompress(compressedData)!
        
        // 验证数据完整性
        XCTAssertEqual(originalStroke.points.count, decompressedStroke.points.count)
        
        for (original, decompressed) in zip(originalStroke.points, decompressedStroke.points) {
            XCTAssertEqual(original.x, decompressed.x, accuracy: 0.1)
            XCTAssertEqual(original.y, decompressed.y, accuracy: 0.1)
            XCTAssertEqual(original.pressure, decompressed.pressure, accuracy: 0.01)
        }
    }
}
```

## 性能监控系统

### 1. 实时性能监控
```swift
class PerformanceMonitor {
    private var metrics: [String: [TimeInterval]] = [:]
    private var memorySnapshots: [Int64] = []
    private var frameRates: [Double] = []
    
    func startMeasuring(_ operation: String) {
        // 记录开始时间
    }
    
    func endMeasuring(_ operation: String) {
        // 记录结束时间并计算耗时
    }
    
    func recordMemoryUsage() {
        let currentMemory = getMemoryUsage()
        memorySnapshots.append(currentMemory)
    }
    
    func recordFrameRate(_ fps: Double) {
        frameRates.append(fps)
    }
    
    func generateReport() -> PerformanceReport {
        return PerformanceReport(
            averageLatencies: calculateAverageLatencies(),
            memoryTrend: analyzeMemoryTrend(),
            frameRateStats: analyzeFrameRates()
        )
    }
}
```

### 2. 性能警报系统
```swift
class PerformanceAlertSystem {
    private let thresholds = PerformanceThresholds()
    
    func checkPerformance(_ metrics: PerformanceMetrics) {
        if metrics.inputLatency > thresholds.maxInputLatency {
            triggerAlert(.highInputLatency(metrics.inputLatency))
        }
        
        if metrics.memoryUsage > thresholds.maxMemoryUsage {
            triggerAlert(.highMemoryUsage(metrics.memoryUsage))
        }
        
        if metrics.frameRate < thresholds.minFrameRate {
            triggerAlert(.lowFrameRate(metrics.frameRate))
        }
    }
    
    private func triggerAlert(_ alert: PerformanceAlert) {
        // 发送性能警报
        NotificationCenter.default.post(
            name: .performanceAlert,
            object: alert
        )
    }
}
```

## 测试执行计划

### 1. 开发阶段测试
- **单元测试**: 每次代码提交
- **性能回归测试**: 每日构建
- **内存泄漏检测**: 每周执行
- **压力测试**: 每月执行

### 2. 发布前测试
- **完整性能测试套件**: 发布前2周
- **设备兼容性测试**: 发布前1周
- **用户场景测试**: 发布前3天
- **最终性能验证**: 发布前1天

### 3. 测试环境配置
- **测试设备**: iPad Pro M2, iPad Air 5, iPad 9th Gen
- **测试条件**: 不同电量、温度、后台应用状态
- **测试数据**: 标准化测试文档集合
- **测试工具**: Xcode Instruments, 自定义性能监控工具

### 4. 性能基准验收标准
- **所有核心指标**: 必须达到目标值
- **兼容性测试**: 在所有目标设备上通过
- **压力测试**: 连续运行8小时无崩溃
- **内存测试**: 无明显内存泄漏

这个测试计划确保了线条构造器在各种条件下都能保持优秀的性能表现，为用户提供流畅的书写体验。
