//
//  StrokeEngineTests.swift
//  path_generatorTests
//
//  Created by AI Assistant on 2025/5/27.
//

import XCTest
@testable import path_generator

final class StrokeEngineTests: XCTestCase {
    
    var strokeEngine: StrokeEngine!
    var testStyle: StrokeStyle!
    
    override func setUpWithError() throws {
        strokeEngine = StrokeEngine()
        testStyle = .defaultPen
    }
    
    override func tearDownWithError() throws {
        strokeEngine = nil
        testStyle = nil
    }
    
    // MARK: - 基础功能测试
    
    func testStrokeCreation() throws {
        // 测试线条创建
        let startPoint = CGPoint(x: 100, y: 100)
        let pressure: Float = 0.5
        
        strokeEngine.startStroke(at: startPoint, pressure: pressure, style: testStyle)
        
        XCTAssertNotNil(strokeEngine.currentStroke)
        XCTAssertTrue(strokeEngine.isProcessing)
        XCTAssertEqual(strokeEngine.currentStroke?.pointCount, 1)
    }
    
    func testStrokePointAddition() throws {
        // 测试添加点
        strokeEngine.startStroke(at: CGPoint(x: 100, y: 100), pressure: 0.5, style: testStyle)
        
        strokeEngine.addPoint(at: CGPoint(x: 110, y: 110), pressure: 0.6)
        strokeEngine.addPoint(at: CGPoint(x: 120, y: 120), pressure: 0.7)
        
        XCTAssertEqual(strokeEngine.currentStroke?.pointCount, 3)
    }
    
    func testStrokeCompletion() throws {
        // 测试线条完成
        strokeEngine.startStroke(at: CGPoint(x: 100, y: 100), pressure: 0.5, style: testStyle)
        strokeEngine.addPoint(at: CGPoint(x: 110, y: 110), pressure: 0.6)
        strokeEngine.endStroke()
        
        XCTAssertNil(strokeEngine.currentStroke)
        XCTAssertFalse(strokeEngine.isProcessing)
        XCTAssertEqual(strokeEngine.completedStrokes.count, 1)
    }
    
    func testStrokeCancellation() throws {
        // 测试线条取消
        strokeEngine.startStroke(at: CGPoint(x: 100, y: 100), pressure: 0.5, style: testStyle)
        strokeEngine.addPoint(at: CGPoint(x: 110, y: 110), pressure: 0.6)
        strokeEngine.cancelStroke()
        
        XCTAssertNil(strokeEngine.currentStroke)
        XCTAssertFalse(strokeEngine.isProcessing)
        XCTAssertEqual(strokeEngine.completedStrokes.count, 0)
    }
    
    // MARK: - 性能测试
    
    func testStrokePerformance() throws {
        // 测试大量点的处理性能
        measure {
            strokeEngine.startStroke(at: CGPoint(x: 0, y: 0), pressure: 0.5, style: testStyle)
            
            for i in 1...1000 {
                strokeEngine.addPoint(at: CGPoint(x: i, y: i), pressure: Float(i % 100) / 100.0)
            }
            
            strokeEngine.endStroke()
        }
    }
    
    func testMultipleStrokesPerformance() throws {
        // 测试多个线条的性能
        measure {
            for strokeIndex in 0..<10 {
                strokeEngine.startStroke(at: CGPoint(x: strokeIndex * 10, y: 0), pressure: 0.5, style: testStyle)
                
                for i in 1...100 {
                    strokeEngine.addPoint(at: CGPoint(x: strokeIndex * 10 + i, y: i), pressure: 0.5)
                }
                
                strokeEngine.endStroke()
            }
        }
    }
    
    // MARK: - 过滤测试
    
    func testPointFiltering() throws {
        // 测试点过滤功能
        strokeEngine.startStroke(at: CGPoint(x: 100, y: 100), pressure: 0.5, style: testStyle)
        
        // 添加很多相近的点
        for i in 0..<100 {
            strokeEngine.addPoint(at: CGPoint(x: 100 + i/10, y: 100), pressure: 0.5)
        }
        
        strokeEngine.endStroke()
        
        // 验证过滤后的点数量应该少于原始点数量
        let completedStroke = strokeEngine.completedStrokes.first!
        XCTAssertLessThan(completedStroke.pointCount, 100)
    }
    
    // MARK: - 统计测试
    
    func testStatistics() throws {
        // 测试统计功能
        strokeEngine.startStroke(at: CGPoint(x: 100, y: 100), pressure: 0.5, style: testStyle)
        strokeEngine.addPoint(at: CGPoint(x: 110, y: 110), pressure: 0.6)
        strokeEngine.endStroke()
        
        strokeEngine.startStroke(at: CGPoint(x: 200, y: 200), pressure: 0.7, style: testStyle)
        strokeEngine.addPoint(at: CGPoint(x: 210, y: 210), pressure: 0.8)
        strokeEngine.endStroke()
        
        let stats = strokeEngine.statistics
        XCTAssertEqual(stats.totalStrokes, 2)
        XCTAssertEqual(stats.currentStrokePoints, 0)
        XCTAssertFalse(stats.isDrawing)
    }
    
    // MARK: - 撤销测试
    
    func testUndo() throws {
        // 测试撤销功能
        strokeEngine.startStroke(at: CGPoint(x: 100, y: 100), pressure: 0.5, style: testStyle)
        strokeEngine.endStroke()
        
        strokeEngine.startStroke(at: CGPoint(x: 200, y: 200), pressure: 0.5, style: testStyle)
        strokeEngine.endStroke()
        
        XCTAssertEqual(strokeEngine.completedStrokes.count, 2)
        
        strokeEngine.undoLastStroke()
        XCTAssertEqual(strokeEngine.completedStrokes.count, 1)
        
        strokeEngine.undoLastStroke()
        XCTAssertEqual(strokeEngine.completedStrokes.count, 0)
    }
    
    // MARK: - 清空测试
    
    func testClearAll() throws {
        // 测试清空所有线条
        strokeEngine.startStroke(at: CGPoint(x: 100, y: 100), pressure: 0.5, style: testStyle)
        strokeEngine.endStroke()
        
        strokeEngine.startStroke(at: CGPoint(x: 200, y: 200), pressure: 0.5, style: testStyle)
        strokeEngine.endStroke()
        
        XCTAssertEqual(strokeEngine.completedStrokes.count, 2)
        
        strokeEngine.clearAllStrokes()
        XCTAssertEqual(strokeEngine.completedStrokes.count, 0)
    }
    
    // MARK: - 边界条件测试
    
    func testEmptyStroke() throws {
        // 测试空线条
        strokeEngine.startStroke(at: CGPoint(x: 100, y: 100), pressure: 0.5, style: testStyle)
        strokeEngine.endStroke()
        
        XCTAssertEqual(strokeEngine.completedStrokes.count, 1)
        XCTAssertEqual(strokeEngine.completedStrokes.first?.pointCount, 1)
    }
    
    func testInvalidPressure() throws {
        // 测试无效压力值
        strokeEngine.startStroke(at: CGPoint(x: 100, y: 100), pressure: -1.0, style: testStyle)
        strokeEngine.addPoint(at: CGPoint(x: 110, y: 110), pressure: 2.0)
        strokeEngine.endStroke()
        
        let stroke = strokeEngine.completedStrokes.first!
        // 验证压力值被限制在有效范围内
        for point in stroke.points {
            XCTAssertGreaterThanOrEqual(point.pressure, 0.0)
            XCTAssertLessThanOrEqual(point.pressure, 1.0)
        }
    }
    
    func testConcurrentAccess() throws {
        // 测试并发访问
        let expectation = XCTestExpectation(description: "Concurrent access")
        expectation.expectedFulfillmentCount = 10
        
        for i in 0..<10 {
            DispatchQueue.global().async {
                self.strokeEngine.startStroke(at: CGPoint(x: i * 10, y: 0), pressure: 0.5, style: self.testStyle)
                
                for j in 1...10 {
                    self.strokeEngine.addPoint(at: CGPoint(x: i * 10 + j, y: j), pressure: 0.5)
                }
                
                self.strokeEngine.endStroke()
                expectation.fulfill()
            }
        }
        
        wait(for: [expectation], timeout: 5.0)
        
        // 验证所有线条都被正确创建
        XCTAssertEqual(strokeEngine.completedStrokes.count, 10)
    }
}
