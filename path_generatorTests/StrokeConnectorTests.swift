//
//  StrokeConnectorTests.swift
//  path_generatorTests
//
//  Created by AI Assistant on 2025/5/27.
//

import XCTest
@testable import path_generator

final class StrokeConnectorTests: XCTestCase {
    
    var testStyle: StrokeStyle!
    
    override func setUpWithError() throws {
        testStyle = .defaultPen
    }
    
    override func tearDownWithError() throws {
        testStyle = nil
    }
    
    // MARK: - 辅助方法
    
    private func createTestStroke(points: [(Float, Float)], style: StrokeStyle) -> Stroke {
        let stroke = Stroke(style: style)
        for (i, point) in points.enumerated() {
            let strokePoint = StrokePoint(
                x: point.0,
                y: point.1,
                pressure: 0.5,
                timestamp: TimeInterval(i) * 0.1
            )
            stroke.addPoint(strokePoint)
        }
        stroke.complete()
        return stroke
    }
    
    // MARK: - 基础连接测试
    
    func testEndToStartConnection() throws {
        // 测试尾部到头部连接
        let stroke1 = createTestStroke(points: [(0, 0), (10, 0), (20, 0)], style: testStyle)
        let stroke2 = createTestStroke(points: [(25, 0), (35, 0), (45, 0)], style: testStyle)
        
        let result = StrokeConnector.connectStrokes(stroke1, stroke2)
        
        XCTAssertTrue(result.success)
        XCTAssertEqual(result.connectionType, .endToStart)
        XCTAssertNotNil(result.connectedStroke)
        
        // 验证连接后的线条点数
        let expectedPointCount = stroke1.pointCount + stroke2.pointCount + 1 // +1 for connection point
        XCTAssertEqual(result.connectedStroke?.pointCount, expectedPointCount)
    }
    
    func testEndToEndConnection() throws {
        // 测试尾部到尾部连接
        let stroke1 = createTestStroke(points: [(0, 0), (10, 0), (20, 0)], style: testStyle)
        let stroke2 = createTestStroke(points: [(45, 0), (35, 0), (25, 0)], style: testStyle)
        
        let result = StrokeConnector.connectStrokes(stroke1, stroke2)
        
        XCTAssertTrue(result.success)
        XCTAssertEqual(result.connectionType, .endToEnd)
        XCTAssertNotNil(result.connectedStroke)
    }
    
    func testStartToStartConnection() throws {
        // 测试头部到头部连接
        let stroke1 = createTestStroke(points: [(20, 0), (10, 0), (0, 0)], style: testStyle)
        let stroke2 = createTestStroke(points: [(25, 0), (35, 0), (45, 0)], style: testStyle)
        
        let result = StrokeConnector.connectStrokes(stroke1, stroke2)
        
        XCTAssertTrue(result.success)
        XCTAssertEqual(result.connectionType, .startToStart)
        XCTAssertNotNil(result.connectedStroke)
    }
    
    func testStartToEndConnection() throws {
        // 测试头部到尾部连接
        let stroke1 = createTestStroke(points: [(25, 0), (15, 0), (5, 0)], style: testStyle)
        let stroke2 = createTestStroke(points: [(0, 0), (10, 0), (20, 0)], style: testStyle)
        
        let result = StrokeConnector.connectStrokes(stroke1, stroke2)
        
        XCTAssertTrue(result.success)
        XCTAssertEqual(result.connectionType, .startToEnd)
        XCTAssertNotNil(result.connectedStroke)
    }
    
    // MARK: - 连接失败测试
    
    func testDistanceTooFar() throws {
        // 测试距离过远的情况
        let stroke1 = createTestStroke(points: [(0, 0), (10, 0), (20, 0)], style: testStyle)
        let stroke2 = createTestStroke(points: [(100, 0), (110, 0), (120, 0)], style: testStyle)
        
        let result = StrokeConnector.connectStrokes(stroke1, stroke2)
        
        XCTAssertFalse(result.success)
        XCTAssertEqual(result.connectionType, .none)
        XCTAssertNil(result.connectedStroke)
    }
    
    func testIncompatibleStyles() throws {
        // 测试不兼容的样式
        let style1 = StrokeStyle.defaultPen
        let style2 = StrokeStyle.defaultBrush
        
        let stroke1 = createTestStroke(points: [(0, 0), (10, 0), (20, 0)], style: style1)
        let stroke2 = createTestStroke(points: [(25, 0), (35, 0), (45, 0)], style: style2)
        
        let result = StrokeConnector.connectStrokes(stroke1, stroke2)
        
        XCTAssertFalse(result.success)
    }
    
    func testAngleTooLarge() throws {
        // 测试角度过大的情况
        let stroke1 = createTestStroke(points: [(0, 0), (10, 0), (20, 0)], style: testStyle)
        let stroke2 = createTestStroke(points: [(25, 0), (25, 10), (25, 20)], style: testStyle)
        
        let result = StrokeConnector.connectStrokes(stroke1, stroke2)
        
        // 根据角度阈值，这个连接可能会失败
        if !result.success {
            XCTAssertEqual(result.connectionType, .none)
        }
    }
    
    // MARK: - 自动连接测试
    
    func testAutoConnectMultipleStrokes() throws {
        // 测试自动连接多个线条
        let strokes = [
            createTestStroke(points: [(0, 0), (10, 0)], style: testStyle),
            createTestStroke(points: [(15, 0), (25, 0)], style: testStyle),
            createTestStroke(points: [(30, 0), (40, 0)], style: testStyle)
        ]
        
        let connectedStrokes = StrokeConnector.autoConnectStrokes(strokes)
        
        // 应该连接成更少的线条
        XCTAssertLessThan(connectedStrokes.count, strokes.count)
    }
    
    func testAutoConnectNoConnections() throws {
        // 测试没有可连接线条的情况
        let strokes = [
            createTestStroke(points: [(0, 0), (10, 0)], style: testStyle),
            createTestStroke(points: [(100, 0), (110, 0)], style: testStyle),
            createTestStroke(points: [(200, 0), (210, 0)], style: testStyle)
        ]
        
        let connectedStrokes = StrokeConnector.autoConnectStrokes(strokes)
        
        // 应该保持原有数量
        XCTAssertEqual(connectedStrokes.count, strokes.count)
    }
    
    // MARK: - 候选查找测试
    
    func testFindConnectableCandidates() throws {
        // 测试查找可连接的候选线条
        let targetStroke = createTestStroke(points: [(0, 0), (10, 0), (20, 0)], style: testStyle)
        
        let candidates = [
            createTestStroke(points: [(25, 0), (35, 0)], style: testStyle),      // 可连接
            createTestStroke(points: [(100, 0), (110, 0)], style: testStyle),   // 距离太远
            createTestStroke(points: [(22, 0), (32, 0)], style: testStyle),     // 可连接
        ]
        
        let connectableCandidates = StrokeConnector.findConnectableCandidates(for: targetStroke, in: candidates)
        
        XCTAssertEqual(connectableCandidates.count, 2)
    }
    
    // MARK: - 性能测试
    
    func testConnectionPerformance() throws {
        // 测试连接性能
        let stroke1 = createTestStroke(points: Array(0..<1000).map { (Float($0), 0) }, style: testStyle)
        let stroke2 = createTestStroke(points: Array(1005..<2005).map { (Float($0), 0) }, style: testStyle)
        
        measure {
            let _ = StrokeConnector.connectStrokes(stroke1, stroke2)
        }
    }
    
    func testAutoConnectPerformance() throws {
        // 测试自动连接性能
        let strokes = (0..<50).map { i in
            createTestStroke(points: [(Float(i * 25), 0), (Float(i * 25 + 20), 0)], style: testStyle)
        }
        
        measure {
            let _ = StrokeConnector.autoConnectStrokes(strokes)
        }
    }
    
    // MARK: - 边界条件测试
    
    func testEmptyStrokes() throws {
        // 测试空线条
        let stroke1 = Stroke(style: testStyle)
        let stroke2 = Stroke(style: testStyle)
        
        let result = StrokeConnector.connectStrokes(stroke1, stroke2)
        
        XCTAssertFalse(result.success)
    }
    
    func testSinglePointStrokes() throws {
        // 测试单点线条
        let stroke1 = createTestStroke(points: [(0, 0)], style: testStyle)
        let stroke2 = createTestStroke(points: [(5, 0)], style: testStyle)
        
        let result = StrokeConnector.connectStrokes(stroke1, stroke2)
        
        XCTAssertTrue(result.success)
        XCTAssertNotNil(result.connectedStroke)
    }
    
    func testIdenticalStrokes() throws {
        // 测试相同的线条
        let stroke1 = createTestStroke(points: [(0, 0), (10, 0), (20, 0)], style: testStyle)
        let stroke2 = createTestStroke(points: [(0, 0), (10, 0), (20, 0)], style: testStyle)
        
        let result = StrokeConnector.connectStrokes(stroke1, stroke2)
        
        // 相同位置的线条应该能够连接
        XCTAssertTrue(result.success)
    }
}
