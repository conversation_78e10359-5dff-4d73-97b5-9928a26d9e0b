//
//  StrokePointTests.swift
//  path_generatorTests
//
//  Created by AI Assistant on 2025/5/26.
//

import XCTest
@testable import path_generator

class StrokePointTests: XCTestCase {
    
    // MARK: - 基础功能测试
    func testStrokePointInitialization() {
        let point = StrokePoint(x: 100.5, y: 200.7, pressure: 0.8, timestamp: 1.5)
        
        XCTAssertEqual(point.x, 100.5, accuracy: 0.1, "X coordinate should be preserved")
        XCTAssertEqual(point.y, 200.7, accuracy: 0.1, "Y coordinate should be preserved")
        XCTAssertEqual(point.pressure, 0.8, accuracy: 0.01, "Pressure should be preserved")
        XCTAssertEqual(point.timestamp, 1.5, accuracy: 0.001, "Timestamp should be preserved")
    }
    
    func testStrokePointCGPointInitialization() {
        let cgPoint = CGPoint(x: 150.3, y: 250.9)
        let point = StrokePoint(point: cgPoint, pressure: 0.6, timestamp: 2.3)
        
        XCTAssertEqual(point.x, 150.3, accuracy: 0.1, "X coordinate should match CGPoint")
        XCTAssertEqual(point.y, 250.9, accuracy: 0.1, "Y coordinate should match CGPoint")
        XCTAssertEqual(point.pressure, 0.6, accuracy: 0.01, "Pressure should be preserved")
    }
    
    func testStrokePointEquality() {
        let point1 = StrokePoint(x: 100, y: 200, pressure: 0.5, timestamp: 1.0)
        let point2 = StrokePoint(x: 100, y: 200, pressure: 0.5, timestamp: 1.0)
        let point3 = StrokePoint(x: 101, y: 200, pressure: 0.5, timestamp: 1.0)
        
        XCTAssertEqual(point1, point2, "Identical points should be equal")
        XCTAssertNotEqual(point1, point3, "Different points should not be equal")
    }
    
    // MARK: - 内存优化测试
    func testMemoryFootprint() {
        let point = StrokePoint(x: 100, y: 200, pressure: 0.5, timestamp: 1.0)
        let size = MemoryLayout.size(ofValue: point)
        
        // 验证内存占用为7字节 (2+2+1+2)
        XCTAssertEqual(size, 7, "StrokePoint should occupy exactly 7 bytes")
    }
    
    func testPressureQuantization() {
        // 测试压力值的量化精度
        let point1 = StrokePoint(x: 0, y: 0, pressure: 0.0, timestamp: 0)
        let point2 = StrokePoint(x: 0, y: 0, pressure: 1.0, timestamp: 0)
        let point3 = StrokePoint(x: 0, y: 0, pressure: 0.5, timestamp: 0)
        
        XCTAssertEqual(point1.pressure, 0.0, accuracy: 0.01, "Minimum pressure should be preserved")
        XCTAssertEqual(point2.pressure, 1.0, accuracy: 0.01, "Maximum pressure should be preserved")
        XCTAssertEqual(point3.pressure, 0.5, accuracy: 0.01, "Mid pressure should be preserved")
    }
    
    func testTimestampQuantization() {
        // 测试时间戳的量化精度 (毫秒级)
        let point1 = StrokePoint(x: 0, y: 0, pressure: 0.5, timestamp: 0.0)
        let point2 = StrokePoint(x: 0, y: 0, pressure: 0.5, timestamp: 65.535) // 最大值
        let point3 = StrokePoint(x: 0, y: 0, pressure: 0.5, timestamp: 1.234)
        
        XCTAssertEqual(point1.timestamp, 0.0, accuracy: 0.001, "Zero timestamp should be preserved")
        XCTAssertEqual(point2.timestamp, 65.535, accuracy: 0.001, "Max timestamp should be preserved")
        XCTAssertEqual(point3.timestamp, 1.234, accuracy: 0.001, "Fractional timestamp should be preserved")
    }
    
    // MARK: - SIMD 向量运算测试
    func testPositionVector() {
        let point = StrokePoint(x: 100.5, y: 200.7, pressure: 0.5, timestamp: 1.0)
        let position = point.position
        
        XCTAssertEqual(position.x, 100.5, accuracy: 0.1, "Position X should match point X")
        XCTAssertEqual(position.y, 200.7, accuracy: 0.1, "Position Y should match point Y")
    }
    
    func testDistanceCalculation() {
        let point1 = StrokePoint(x: 0, y: 0, pressure: 0.5, timestamp: 1.0)
        let point2 = StrokePoint(x: 3, y: 4, pressure: 0.5, timestamp: 2.0)
        
        let distance = point1.distance(to: point2)
        XCTAssertEqual(distance, 5.0, accuracy: 0.01, "Distance should be calculated correctly (3-4-5 triangle)")
    }
    
    func testDistanceSquaredCalculation() {
        let point1 = StrokePoint(x: 0, y: 0, pressure: 0.5, timestamp: 1.0)
        let point2 = StrokePoint(x: 3, y: 4, pressure: 0.5, timestamp: 2.0)
        
        let distanceSquared = point1.distanceSquared(to: point2)
        XCTAssertEqual(distanceSquared, 25.0, accuracy: 0.01, "Distance squared should be calculated correctly")
    }
    
    func testInterpolation() {
        let point1 = StrokePoint(x: 0, y: 0, pressure: 0.0, timestamp: 1.0)
        let point2 = StrokePoint(x: 10, y: 20, pressure: 1.0, timestamp: 3.0)
        
        let midPoint = point1.interpolate(to: point2, factor: 0.5)
        
        XCTAssertEqual(midPoint.x, 5.0, accuracy: 0.1, "Interpolated X should be at midpoint")
        XCTAssertEqual(midPoint.y, 10.0, accuracy: 0.1, "Interpolated Y should be at midpoint")
        XCTAssertEqual(midPoint.pressure, 0.5, accuracy: 0.01, "Interpolated pressure should be at midpoint")
        XCTAssertEqual(midPoint.timestamp, 2.0, accuracy: 0.001, "Interpolated timestamp should be at midpoint")
    }
    
    func testDirectionCalculation() {
        let point1 = StrokePoint(x: 0, y: 0, pressure: 0.5, timestamp: 1.0)
        let point2 = StrokePoint(x: 10, y: 0, pressure: 0.5, timestamp: 2.0)
        
        let direction = point1.direction(to: point2)
        
        XCTAssertEqual(direction.x, 1.0, accuracy: 0.01, "Direction X should be 1 (rightward)")
        XCTAssertEqual(direction.y, 0.0, accuracy: 0.01, "Direction Y should be 0 (horizontal)")
    }
    
    func testAngleCalculation() {
        let point1 = StrokePoint(x: 0, y: 0, pressure: 0.5, timestamp: 1.0)
        let point2 = StrokePoint(x: 1, y: 1, pressure: 0.5, timestamp: 2.0)
        
        let angle = point1.angle(to: point2)
        
        XCTAssertEqual(angle, Float.pi / 4, accuracy: 0.01, "Angle should be 45 degrees (π/4 radians)")
    }
    
    // MARK: - 几何计算测试
    func testAngleChange() {
        let point1 = StrokePoint(x: 0, y: 0, pressure: 0.5, timestamp: 1.0)
        let point2 = StrokePoint(x: 1, y: 0, pressure: 0.5, timestamp: 2.0)
        let point3 = StrokePoint(x: 1, y: 1, pressure: 0.5, timestamp: 3.0)
        
        let angleChange = point2.angleChange(previous: point1, next: point3)
        
        XCTAssertEqual(angleChange, Float.pi / 2, accuracy: 0.01, "Angle change should be 90 degrees")
    }
    
    func testCollinearityDetection() {
        // 测试共线点
        let point1 = StrokePoint(x: 0, y: 0, pressure: 0.5, timestamp: 1.0)
        let point2 = StrokePoint(x: 1, y: 1, pressure: 0.5, timestamp: 2.0)
        let point3 = StrokePoint(x: 2, y: 2, pressure: 0.5, timestamp: 3.0)
        
        let isCollinear = point2.isCollinear(with: point1, and: point3, tolerance: 0.1)
        XCTAssertTrue(isCollinear, "Points on a straight line should be detected as collinear")
        
        // 测试非共线点
        let point4 = StrokePoint(x: 2, y: 3, pressure: 0.5, timestamp: 3.0)
        let isNotCollinear = point2.isCollinear(with: point1, and: point4, tolerance: 0.1)
        XCTAssertFalse(isNotCollinear, "Points not on a straight line should not be detected as collinear")
    }
    
    // MARK: - CGPoint 转换测试
    func testCGPointConversion() {
        let point = StrokePoint(x: 123.45, y: 678.90, pressure: 0.5, timestamp: 1.0)
        let cgPoint = point.cgPoint
        
        XCTAssertEqual(cgPoint.x, 123.45, accuracy: 0.1, "CGPoint X should match StrokePoint X")
        XCTAssertEqual(cgPoint.y, 678.90, accuracy: 0.1, "CGPoint Y should match StrokePoint Y")
    }
    
    // MARK: - 编码/解码测试
    func testCodableSupport() throws {
        let originalPoint = StrokePoint(x: 100.5, y: 200.7, pressure: 0.8, timestamp: 1.5)
        
        // 编码
        let encoder = JSONEncoder()
        let data = try encoder.encode(originalPoint)
        
        // 解码
        let decoder = JSONDecoder()
        let decodedPoint = try decoder.decode(StrokePoint.self, from: data)
        
        // 验证
        XCTAssertEqual(originalPoint, decodedPoint, "Decoded point should match original")
    }
    
    // MARK: - 性能测试
    func testPerformanceDistanceCalculation() {
        let point1 = StrokePoint(x: 0, y: 0, pressure: 0.5, timestamp: 1.0)
        let point2 = StrokePoint(x: 100, y: 100, pressure: 0.5, timestamp: 2.0)
        
        measure {
            for _ in 0..<10000 {
                _ = point1.distance(to: point2)
            }
        }
    }
    
    func testPerformanceInterpolation() {
        let point1 = StrokePoint(x: 0, y: 0, pressure: 0.0, timestamp: 1.0)
        let point2 = StrokePoint(x: 100, y: 100, pressure: 1.0, timestamp: 2.0)
        
        measure {
            for i in 0..<1000 {
                let factor = Float(i) / 1000.0
                _ = point1.interpolate(to: point2, factor: factor)
            }
        }
    }
}
