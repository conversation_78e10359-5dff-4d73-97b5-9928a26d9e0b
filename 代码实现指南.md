# 代码实现指南

## 文件结构规划

```
path_generator/
├── Models/
│   ├── StrokePoint.swift           // 核心点数据模型
│   ├── Stroke.swift                // 线条数据模型
│   ├── StrokeStyle.swift           // 笔刷样式模型
│   └── StrokeSegment.swift         // 线条分段模型
├── Engines/
│   ├── StrokeEngine.swift          // 线条处理引擎
│   ├── RenderEngine.swift          // 渲染引擎
│   ├── EraserEngine.swift          // 橡皮引擎
│   └── FilterEngine.swift          // 点过滤引擎
├── Rendering/
│   ├── MetalRenderer.swift         // Metal渲染器
│   ├── StrokeShaders.metal         // Metal着色器
│   ├── CanvasView.swift            // 画布视图
│   └── ViewportManager.swift       // 视口管理
├── Storage/
│   ├── DataManager.swift           // 数据管理器
│   ├── StrokeCompressor.swift      // 压缩器
│   ├── IncrementalStorage.swift    // 增量存储
│   └── FileFormat.swift            // 文件格式定义
├── Optimization/
│   ├── MemoryPool.swift            // 内存池
│   ├── LRUCache.swift              // LRU缓存
│   ├── SpatialIndex.swift          // 空间索引
│   └── PerformanceMonitor.swift    // 性能监控
├── UI/
│   ├── DrawingCanvas.swift         // 绘图画布
│   ├── ToolPanel.swift             // 工具面板
│   ├── SettingsView.swift          // 设置界面
│   └── ColorPicker.swift           // 颜色选择器
└── Utils/
    ├── MathUtils.swift             // 数学工具
    ├── GeometryUtils.swift         // 几何工具
    └── Extensions.swift            // 扩展方法
```

## 核心实现要点

### 1. 数据模型实现要点

#### StrokePoint.swift
```swift
import Foundation
import simd

// 高度优化的点数据结构
struct StrokePoint: Codable {
    // 使用Float16节省内存，精度足够
    private let _x: UInt16
    private let _y: UInt16
    private let _pressure: UInt8
    private let _timestamp: UInt16
    
    // 计算属性提供Float接口
    var x: Float { Float16(bitPattern: _x).floatValue }
    var y: Float { Float16(bitPattern: _y).floatValue }
    var pressure: Float { Float(_pressure) / 255.0 }
    var timestamp: TimeInterval { TimeInterval(_timestamp) / 1000.0 }
    
    init(x: Float, y: Float, pressure: Float, timestamp: TimeInterval) {
        self._x = Float16(x).bitPattern
        self._y = Float16(y).bitPattern
        self._pressure = UInt8(max(0, min(255, pressure * 255)))
        self._timestamp = UInt16(max(0, min(65535, timestamp * 1000)))
    }
}

// SIMD优化的向量运算
extension StrokePoint {
    var position: simd_float2 { simd_float2(x, y) }
    
    func distance(to other: StrokePoint) -> Float {
        return simd_distance(self.position, other.position)
    }
    
    func interpolate(to other: StrokePoint, factor: Float) -> StrokePoint {
        let pos = simd_mix(self.position, other.position, factor)
        let pressure = self.pressure + (other.pressure - self.pressure) * factor
        let timestamp = self.timestamp + (other.timestamp - self.timestamp) * Double(factor)
        return StrokePoint(x: pos.x, y: pos.y, pressure: pressure, timestamp: timestamp)
    }
}
```

#### Stroke.swift
```swift
import Foundation
import CoreGraphics

class Stroke: ObservableObject, Identifiable {
    let id = UUID()
    @Published var points: [StrokePoint] = []
    @Published var style: StrokeStyle
    @Published var segments: [StrokeSegment] = []
    
    private var _boundingBox: CGRect?
    
    var boundingBox: CGRect {
        if _boundingBox == nil {
            _boundingBox = calculateBoundingBox()
        }
        return _boundingBox!
    }
    
    init(style: StrokeStyle) {
        self.style = style
    }
    
    func addPoint(_ point: StrokePoint) {
        points.append(point)
        _boundingBox = nil // 失效缓存
        
        // 实时创建线段用于擦除
        if points.count >= 2 {
            updateSegments()
        }
    }
    
    private func calculateBoundingBox() -> CGRect {
        guard !points.isEmpty else { return .zero }
        
        let xs = points.map { $0.x }
        let ys = points.map { $0.y }
        
        let minX = xs.min()!
        let maxX = xs.max()!
        let minY = ys.min()!
        let maxY = ys.max()!
        
        // 考虑线条宽度
        let maxWidth = style.baseWidth * 2
        return CGRect(
            x: CGFloat(minX - maxWidth),
            y: CGFloat(minY - maxWidth),
            width: CGFloat(maxX - minX + maxWidth * 2),
            height: CGFloat(maxY - minY + maxWidth * 2)
        )
    }
    
    private func updateSegments() {
        // 每10个点创建一个段，便于擦除
        let segmentSize = 10
        let newSegmentIndex = (points.count - 1) / segmentSize
        
        if segments.count <= newSegmentIndex {
            let startIndex = newSegmentIndex * segmentSize
            let endIndex = min(startIndex + segmentSize - 1, points.count - 1)
            
            let segment = StrokeSegment(
                startIndex: startIndex,
                endIndex: endIndex,
                isErased: false
            )
            segments.append(segment)
        }
    }
}
```

### 2. 引擎实现要点

#### StrokeEngine.swift
```swift
import Foundation
import Combine
import UIKit

class StrokeEngine: ObservableObject {
    @Published var currentStroke: Stroke?
    @Published var completedStrokes: [Stroke] = []
    
    private let filterEngine = FilterEngine()
    private let memoryPool = MemoryPool.shared
    private var lastPoint: StrokePoint?
    private var pointBuffer: [StrokePoint] = []
    
    // 性能监控
    private let performanceMonitor = PerformanceMonitor()
    
    func startStroke(at point: CGPoint, pressure: Float, style: StrokeStyle) {
        performanceMonitor.startMeasuring("stroke_creation")
        
        // 从内存池获取线条对象
        currentStroke = memoryPool.borrowStroke(style: style)
        
        let strokePoint = StrokePoint(
            x: Float(point.x),
            y: Float(point.y),
            pressure: pressure,
            timestamp: CACurrentMediaTime()
        )
        
        currentStroke?.addPoint(strokePoint)
        lastPoint = strokePoint
        pointBuffer.removeAll(keepingCapacity: true)
        
        performanceMonitor.endMeasuring("stroke_creation")
    }
    
    func addPoint(at point: CGPoint, pressure: Float) {
        guard let stroke = currentStroke else { return }
        
        performanceMonitor.startMeasuring("point_processing")
        
        let strokePoint = StrokePoint(
            x: Float(point.x),
            y: Float(point.y),
            pressure: pressure,
            timestamp: CACurrentMediaTime()
        )
        
        // 实时过滤
        if shouldAddPoint(strokePoint) {
            pointBuffer.append(strokePoint)
            
            // 批量处理以提高性能
            if pointBuffer.count >= 3 {
                let filteredPoints = filterEngine.filterPoints(pointBuffer)
                filteredPoints.forEach { stroke.addPoint($0) }
                pointBuffer.removeAll(keepingCapacity: true)
            }
        }
        
        lastPoint = strokePoint
        performanceMonitor.endMeasuring("point_processing")
    }
    
    func endStroke() {
        guard let stroke = currentStroke else { return }
        
        performanceMonitor.startMeasuring("stroke_completion")
        
        // 处理剩余的缓冲点
        if !pointBuffer.isEmpty {
            let filteredPoints = filterEngine.filterPoints(pointBuffer)
            filteredPoints.forEach { stroke.addPoint($0) }
            pointBuffer.removeAll()
        }
        
        // 最终优化
        optimizeStroke(stroke)
        
        completedStrokes.append(stroke)
        currentStroke = nil
        lastPoint = nil
        
        performanceMonitor.endMeasuring("stroke_completion")
        performanceMonitor.logPerformance()
    }
    
    private func shouldAddPoint(_ point: StrokePoint) -> Bool {
        guard let last = lastPoint else { return true }
        
        // 距离过滤
        let distance = point.distance(to: last)
        if distance < 2.0 { return false }
        
        // 时间过滤
        let timeDelta = point.timestamp - last.timestamp
        if timeDelta < 0.008 { return false } // 125 FPS最大
        
        return true
    }
    
    private func optimizeStroke(_ stroke: Stroke) {
        // Douglas-Peucker算法优化
        let optimizedPoints = filterEngine.douglasPeucker(
            stroke.points,
            tolerance: 1.0
        )
        stroke.points = optimizedPoints
    }
}
```

### 3. 渲染实现要点

#### MetalRenderer.swift
```swift
import Metal
import MetalKit
import simd

class MetalRenderer {
    private let device: MTLDevice
    private let commandQueue: MTLCommandQueue
    private let pipelineState: MTLRenderPipelineState
    private let vertexBuffer: MTLBuffer
    
    // 顶点数据结构
    struct Vertex {
        let position: simd_float2
        let color: simd_float4
        let width: Float
    }
    
    init() throws {
        guard let device = MTLCreateSystemDefaultDevice() else {
            throw RendererError.deviceCreationFailed
        }
        
        self.device = device
        
        guard let commandQueue = device.makeCommandQueue() else {
            throw RendererError.commandQueueCreationFailed
        }
        self.commandQueue = commandQueue
        
        // 创建渲染管线
        let library = device.makeDefaultLibrary()
        let vertexFunction = library?.makeFunction(name: "stroke_vertex")
        let fragmentFunction = library?.makeFunction(name: "stroke_fragment")
        
        let pipelineDescriptor = MTLRenderPipelineDescriptor()
        pipelineDescriptor.vertexFunction = vertexFunction
        pipelineDescriptor.fragmentFunction = fragmentFunction
        pipelineDescriptor.colorAttachments[0].pixelFormat = .bgra8Unorm
        
        // 启用混合
        pipelineDescriptor.colorAttachments[0].isBlendingEnabled = true
        pipelineDescriptor.colorAttachments[0].rgbBlendOperation = .add
        pipelineDescriptor.colorAttachments[0].sourceRGBBlendFactor = .sourceAlpha
        pipelineDescriptor.colorAttachments[0].destinationRGBBlendFactor = .oneMinusSourceAlpha
        
        self.pipelineState = try device.makeRenderPipelineState(descriptor: pipelineDescriptor)
        
        // 创建顶点缓冲区
        self.vertexBuffer = device.makeBuffer(length: 1024 * 1024, options: .storageModeShared)!
    }
    
    func render(strokes: [Stroke], in view: MTKView) {
        guard let drawable = view.currentDrawable,
              let renderPassDescriptor = view.currentRenderPassDescriptor else { return }
        
        let commandBuffer = commandQueue.makeCommandBuffer()!
        let renderEncoder = commandBuffer.makeRenderCommandEncoder(descriptor: renderPassDescriptor)!
        
        renderEncoder.setRenderPipelineState(pipelineState)
        
        for stroke in strokes {
            renderStroke(stroke, encoder: renderEncoder)
        }
        
        renderEncoder.endEncoding()
        commandBuffer.present(drawable)
        commandBuffer.commit()
    }
    
    private func renderStroke(_ stroke: Stroke, encoder: MTLRenderCommandEncoder) {
        let vertices = generateVertices(for: stroke)
        
        // 更新顶点缓冲区
        let vertexPointer = vertexBuffer.contents().bindMemory(to: Vertex.self, capacity: vertices.count)
        vertexPointer.initialize(from: vertices, count: vertices.count)
        
        encoder.setVertexBuffer(vertexBuffer, offset: 0, index: 0)
        encoder.drawPrimitives(type: .triangleStrip, vertexStart: 0, vertexCount: vertices.count)
    }
    
    private func generateVertices(for stroke: Stroke) -> [Vertex] {
        var vertices: [Vertex] = []
        let points = stroke.points
        
        guard points.count >= 2 else { return vertices }
        
        for i in 0..<(points.count - 1) {
            let current = points[i]
            let next = points[i + 1]
            
            // 计算线条方向和法向量
            let direction = simd_normalize(next.position - current.position)
            let normal = simd_float2(-direction.y, direction.x)
            
            // 根据压感计算宽度
            let width = stroke.style.baseWidth * (0.3 + 0.7 * current.pressure)
            let halfWidth = width * 0.5
            
            // 生成四边形的四个顶点
            let color = stroke.style.color.toFloat4()
            
            vertices.append(Vertex(
                position: current.position + normal * halfWidth,
                color: color,
                width: width
            ))
            
            vertices.append(Vertex(
                position: current.position - normal * halfWidth,
                color: color,
                width: width
            ))
        }
        
        return vertices
    }
}

enum RendererError: Error {
    case deviceCreationFailed
    case commandQueueCreationFailed
}
```

### 4. 存储优化实现要点

#### StrokeCompressor.swift
```swift
import Foundation
import Compression

class StrokeCompressor {
    
    func compress(_ stroke: Stroke) -> Data {
        var data = Data()
        
        // 写入头部信息
        writeHeader(stroke, to: &data)
        
        // 压缩点数据
        let compressedPoints = compressPoints(stroke.points)
        data.append(compressedPoints)
        
        // 压缩样式数据
        let compressedStyle = compressStyle(stroke.style)
        data.append(compressedStyle)
        
        return data
    }
    
    func decompress(_ data: Data) -> Stroke? {
        var offset = 0
        
        // 读取头部
        guard let header = readHeader(from: data, offset: &offset) else { return nil }
        
        // 解压缩点数据
        let pointsData = data.subdata(in: offset..<(offset + Int(header.pointsDataSize)))
        offset += Int(header.pointsDataSize)
        
        guard let points = decompressPoints(pointsData, count: Int(header.pointCount)) else { return nil }
        
        // 解压缩样式数据
        let styleData = data.subdata(in: offset..<data.count)
        guard let style = decompressStyle(styleData) else { return nil }
        
        let stroke = Stroke(style: style)
        stroke.points = points
        return stroke
    }
    
    private func compressPoints(_ points: [StrokePoint]) -> Data {
        var compressed = Data()
        
        guard !points.isEmpty else { return compressed }
        
        // 使用差值编码
        var lastX: Float = 0
        var lastY: Float = 0
        var lastPressure: Float = 0
        var lastTimestamp: TimeInterval = 0
        
        for point in points {
            // 计算差值
            let deltaX = point.x - lastX
            let deltaY = point.y - lastY
            let deltaPressure = point.pressure - lastPressure
            let deltaTimestamp = point.timestamp - lastTimestamp
            
            // 量化并编码
            compressed.append(encodeFloat(deltaX))
            compressed.append(encodeFloat(deltaY))
            compressed.append(encodeFloat(deltaPressure))
            compressed.append(encodeTimeInterval(deltaTimestamp))
            
            lastX = point.x
            lastY = point.y
            lastPressure = point.pressure
            lastTimestamp = point.timestamp
        }
        
        // 使用LZFSE压缩
        return compressed.compressed(using: .lzfse) ?? compressed
    }
    
    private func encodeFloat(_ value: Float) -> Data {
        // 量化到16位整数
        let quantized = Int16(value * 100) // 0.01精度
        return withUnsafeBytes(of: quantized) { Data($0) }
    }
    
    private func encodeTimeInterval(_ value: TimeInterval) -> Data {
        // 量化到毫秒
        let quantized = UInt16(value * 1000)
        return withUnsafeBytes(of: quantized) { Data($0) }
    }
}

struct StrokeHeader {
    let id: UUID
    let pointCount: UInt32
    let pointsDataSize: UInt32
    let styleDataSize: UInt32
    let checksum: UInt32
}
```

## 实现注意事项

### 1. 性能关键点
- 使用SIMD指令加速向量运算
- 实现对象池避免频繁内存分配
- 使用Metal进行GPU加速渲染
- 实现多线程处理管线

### 2. 内存优化
- 使用Float16减少内存占用
- 实现LRU缓存管理热点数据
- 及时释放不可见的线条数据
- 使用弱引用避免循环引用

### 3. 存储优化
- 实现增量存储减少I/O
- 使用压缩算法减少文件大小
- 实现流式加载支持大文件
- 添加数据完整性校验

### 4. 用户体验
- 保持输入延迟<16ms
- 实现平滑的动画过渡
- 提供实时的性能反馈
- 支持撤销/重做操作

这个实现指南提供了详细的代码结构和关键实现要点，为后续的具体编码工作提供了清晰的路线图。
