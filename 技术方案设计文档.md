# 书写类App线条构造器技术方案

## 项目概述
开发一款支持Apple Pencil的高性能书写App，实现流畅的线条绘制、压感响应、部分擦除和数据优化功能。

## 核心架构设计

### 1. 系统架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   UI Layer      │    │  Engine Layer   │    │  Storage Layer  │
│                 │    │                 │    │                 │
│ - CanvasView    │◄──►│ - StrokeEngine  │◄──►│ - DataManager   │
│ - ToolPanel     │    │ - RenderEngine  │    │ - Compressor    │
│ - SettingsView  │    │ - EraserEngine  │    │ - Serializer    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2. 核心组件详细设计

#### 2.1 数据模型层
```swift
// 核心点数据结构 - 高度优化的存储格式
struct StrokePoint {
    let x: Float16          // 2字节，精度足够
    let y: Float16          // 2字节
    let pressure: UInt8     // 1字节，0-255映射到0-1
    let timestamp: UInt16   // 2字节，相对时间戳
    // 总计：7字节/点
}

// 线条数据结构
struct Stroke {
    let id: UUID
    var points: [StrokePoint]
    var style: StrokeStyle
    var boundingBox: CGRect
    var segments: [StrokeSegment]  // 用于部分擦除
}

// 笔刷样式
struct StrokeStyle {
    var baseWidth: Float
    var pressureSensitivity: Float
    var color: UInt32              // ARGB压缩格式
    var brushType: BrushType
    var opacity: UInt8
}
```

#### 2.2 StrokeEngine（线条引擎）
**职责：**
- 实时处理触摸输入
- 压感数据处理和平滑
- 点过滤和优化
- 线条生成和管理

**核心算法：**
1. **压感映射算法**
   ```swift
   func mapPressureToWidth(pressure: Float, baseWidth: Float, sensitivity: Float) -> Float {
       let normalizedPressure = smoothPressure(pressure)
       return baseWidth * (0.3 + 0.7 * pow(normalizedPressure, sensitivity))
   }
   ```

2. **实时点过滤算法**
   - 距离阈值过滤：移除距离<2像素的点
   - 角度过滤：移除角度变化<5度的点
   - 时间窗口合并：50ms内的相似点合并

3. **Douglas-Peucker优化**
   - 自适应阈值：根据缩放级别调整
   - 保留特征点：压力变化>20%的点必须保留

#### 2.3 RenderEngine（渲染引擎）
**技术栈：** Metal + Core Graphics混合渲染

**渲染管线：**
1. **顶点生成阶段**
   - 根据压感生成动态宽度顶点
   - 使用三角带构建线条几何体
   - 实现笔锋和拖尾效果

2. **片段着色阶段**
   - 抗锯齿处理
   - 纹理混合
   - 透明度渐变

**性能优化策略：**
- 视口裁剪：只渲染可见区域
- LOD系统：远距离线条简化渲染
- 批量渲染：合并相同样式的线条
- 双缓冲：避免渲染阻塞

#### 2.4 EraserEngine（橡皮引擎）
**核心思路：** 线条分段 + 空间索引

**数据结构：**
```swift
struct StrokeSegment {
    var startIndex: Int
    var endIndex: Int
    var isErased: Bool
    var boundingBox: CGRect
}

class SpatialIndex {
    private var quadTree: QuadTree<StrokeSegment>
    
    func findIntersectingSegments(eraserPath: CGPath) -> [StrokeSegment]
    func splitSegment(_ segment: StrokeSegment, at intersectionPoints: [CGPoint]) -> [StrokeSegment]
}
```

**擦除算法：**
1. 使用QuadTree快速定位相交线段
2. 精确计算线段与橡皮路径的交点
3. 分割线段并标记擦除状态
4. 重新连接未擦除的线段

## 3. 性能优化方案

### 3.1 内存管理优化
```swift
class StrokePool {
    private var availableStrokes: [Stroke] = []
    private var availablePoints: [StrokePoint] = []
    
    func borrowStroke() -> Stroke
    func returnStroke(_ stroke: Stroke)
    func borrowPoints(count: Int) -> [StrokePoint]
    func returnPoints(_ points: [StrokePoint])
}

class LRUCache<Key: Hashable, Value> {
    private let maxSize: Int
    private var cache: [Key: Value] = [:]
    private var accessOrder: [Key] = []
    
    func get(_ key: Key) -> Value?
    func set(_ key: Key, value: Value)
}
```

### 3.2 渲染性能优化
1. **视口裁剪系统**
   ```swift
   class ViewportManager {
       func getVisibleStrokes(in viewport: CGRect) -> [Stroke]
       func updateVisibility(for strokes: [Stroke])
   }
   ```

2. **LOD（细节层次）系统**
   - 缩放级别 > 2x：显示所有点
   - 缩放级别 1-2x：显示50%的点
   - 缩放级别 < 1x：显示25%的点

3. **预测性渲染**
   ```swift
   class PredictiveRenderer {
       func predictNextPoints(from history: [StrokePoint]) -> [StrokePoint]
       func preRenderPredictedPath()
   }
   ```

### 3.3 多线程优化
```swift
class StrokeProcessor {
    private let inputQueue = DispatchQueue(label: "stroke.input", qos: .userInteractive)
    private let processingQueue = DispatchQueue(label: "stroke.processing", qos: .userInitiated)
    private let renderQueue = DispatchQueue.main
    
    func processInput(_ point: StrokePoint) {
        inputQueue.async { [weak self] in
            // 输入处理
            self?.processingQueue.async {
                // 点过滤和优化
                self?.renderQueue.async {
                    // UI更新
                }
            }
        }
    }
}
```

## 4. 数据存储优化方案

### 4.1 压缩存储格式
**目标：** 将存储大小减少80%以上

**压缩策略：**
1. **坐标压缩**
   - 使用相对坐标差值存储
   - 变长编码（VarInt）压缩小数值
   - 量化精度：保留1/10像素精度

2. **压力数据压缩**
   - 8位量化（0-255）
   - 游程编码压缩连续相同值
   - 差值编码

3. **时间戳压缩**
   - 相对时间戳（相对于线条开始时间）
   - 毫秒精度量化
   - 变长编码

### 4.2 序列化格式设计
```swift
struct CompressedStroke {
    let header: StrokeHeader      // 16字节
    let compressedPoints: Data    // 变长
    let style: StrokeStyle        // 12字节
}

struct StrokeHeader {
    let id: UUID                  // 16字节
    let pointCount: UInt16        // 2字节
    let boundingBox: CompressedRect // 8字节
    let compressionFlags: UInt8   // 1字节
    let checksum: UInt8          // 1字节
}

class StrokeCompressor {
    func compress(_ stroke: Stroke) -> CompressedStroke
    func decompress(_ compressed: CompressedStroke) -> Stroke
    
    private func compressPoints(_ points: [StrokePoint]) -> Data
    private func decompressPoints(_ data: Data) -> [StrokePoint]
}
```

### 4.3 增量存储系统
```swift
class IncrementalStorage {
    private var baseVersion: [Stroke] = []
    private var deltaOperations: [DeltaOperation] = []
    
    enum DeltaOperation {
        case addStroke(Stroke)
        case removeStroke(UUID)
        case modifyStroke(UUID, [StrokePoint])
    }
    
    func saveIncremental(_ operation: DeltaOperation)
    func compactDeltas() // 定期合并增量到基础版本
    func loadVersion(at index: Int) -> [Stroke]
}
```

### 4.4 文件格式设计
```
文件头 (32字节)
├── 魔数 (4字节): "STRK"
├── 版本号 (2字节): 0x0001
├── 线条数量 (4字节)
├── 压缩类型 (1字节)
├── 校验和 (4字节)
└── 保留字段 (17字节)

索引区 (变长)
├── 线条1偏移量 (8字节)
├── 线条1大小 (4字节)
├── 线条2偏移量 (8字节)
└── ...

数据区 (变长)
├── 压缩线条1数据
├── 压缩线条2数据
└── ...
```

## 5. 实现优先级和里程碑

### Phase 1: 基础架构 (Week 1-2)
- [ ] 创建核心数据模型
- [ ] 实现基础触摸处理
- [ ] 搭建Canvas框架
- [ ] 基础线条渲染

### Phase 2: 压感和优化 (Week 3-4)
- [ ] Apple Pencil压感集成
- [ ] 实时点过滤算法
- [ ] 性能优化（内存池、多线程）
- [ ] 基础存储压缩

### Phase 3: 高级功能 (Week 5-6)
- [ ] 部分擦除功能
- [ ] Metal渲染管线
- [ ] 增量存储系统
- [ ] 用户设置界面

### Phase 4: 优化和完善 (Week 7-8)
- [ ] 性能调优
- [ ] 存储格式优化
- [ ] 用户体验优化
- [ ] 测试和调试

## 6. 技术风险和应对策略

### 6.1 性能风险
**风险：** 高频率输入导致性能瓶颈
**应对：** 
- 实现自适应采样率
- 使用预测性渲染
- 多级LOD系统

### 6.2 存储风险
**风险：** 压缩算法影响加载性能
**应对：**
- 实现流式解压缩
- 缓存热点数据
- 后台预加载

### 6.3 兼容性风险
**风险：** 不同设备性能差异
**应对：**
- 设备性能检测
- 动态质量调整
- 降级渲染模式

## 7. 测试策略

### 7.1 性能测试
- 输入延迟测试（目标<16ms）
- 内存使用测试
- 电池消耗测试
- 存储压缩率测试

### 7.2 功能测试
- 压感响应准确性
- 擦除功能完整性
- 数据完整性验证
- 跨设备兼容性

这个方案为后续的具体实现提供了详细的技术路线图。每个组件都有明确的职责和实现细节，便于分步骤开发和优化。
